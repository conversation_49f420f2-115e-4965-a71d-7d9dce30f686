# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Android
*.apk
*.aab
*.ap_
*.dex
*.class
local.properties
captures/
.externalNativeBuild
.cxx

# iOS
*.xcodeproj
*.xcworkspace
!default.xcworkspace
xcuserdata/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.xccheckout
*.xcscmblueprint
*.hmap
*.ipa
*.dSYM.zip
*.dSYM
timeline.xctimeline
playground.xcworkspace
.build/

# Kotlin/Native
*.klib
*.def

# CocoaPods
Pods/
*.podspec
*.lock

# HarmonyOS
harmonyApp/entry/libs/
harmonyApp/entry/src/main/cpp/include/libkn_api.h
harmonyApp/.hvigor/
harmonyApp/build/
harmonyApp/oh_modules/
harmonyApp/local.properties

# Room
shared/schemas/

# Logs
*.log

# Temporary files
*.tmp
*.temp
*~

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# JVM Crash logs
hs_err_pid*

# Augment
.augment/

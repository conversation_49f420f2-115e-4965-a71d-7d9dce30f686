# 小学生作业助手 (HomeworkAssistantKMP)

一个基于 OvCompose 跨平台框架开发的小学生作业助手应用，支持 Android、iOS 和鸿蒙 Next 系统。

## 功能特性

### 📝 听写生字
- 语音播报生字
- 实时语音对话
- 智能指令识别（下个词、上个词、重复等）
- AI 语义理解

### 📖 背诵练习
- 语音识别背诵内容
- 超时自动提示
- 智能完成检测
- 语义指令识别（背完了、重新开始等）

### 📷 拍照检查
- 拍照识别手写内容
- OCR 文字识别
- 原图批改标注
- 生成可读检查结果

### 🎯 智能管理
- 添加和管理生字
- 添加和管理背诵内容
- 学习进度跟踪
- 个性化设置

## 技术架构

### 跨平台框架
- **OvCompose**: 腾讯视频团队开发的跨平台 Compose 框架
- **Kotlin Multiplatform**: 共享业务逻辑
- **Compose Multiplatform**: 统一 UI 框架

### 支持平台
- ✅ Android (API 24+)
- ✅ iOS (iOS 14+)
- ✅ 鸿蒙 Next

### 核心技术栈
- **UI**: Jetpack Compose / Compose Multiplatform
- **架构**: MVVM + Repository Pattern
- **依赖注入**: Koin
- **数据库**: Room + SQLite
- **网络**: Ktor Client
- **序列化**: Kotlinx Serialization
- **导航**: Navigation Compose
- **图像处理**: ML Kit (Android) / Vision (iOS)
- **语音识别**: 平台原生 API

## 项目结构

```
HomeworkAssistantKMP/
├── composeApp/                 # 应用程序入口
│   ├── src/
│   │   ├── androidMain/       # Android 特定代码
│   │   ├── iosMain/           # iOS 特定代码
│   │   └── commonMain/        # 通用 UI 代码
│   └── build.gradle.kts
├── shared/                     # 共享业务逻辑
│   ├── src/
│   │   ├── commonMain/        # 通用代码
│   │   ├── androidMain/       # Android 特定实现
│   │   ├── iosMain/           # iOS 特定实现
│   │   └── ohosArm64Main/     # 鸿蒙特定实现
│   └── build.gradle.kts
├── harmonyApp/                 # 鸿蒙应用项目
├── iosApp/                     # iOS 应用项目
├── gradle/
├── build.gradle.kts
├── settings.gradle.kts
└── README.md
```

## 开发环境要求

### 通用要求
- JDK 11 或更高版本
- Gradle 8.7+
- Kotlin 2.0.21-KBA-005

### Android 开发
- Android Studio Hedgehog (2023.1.1) 或更高版本
- Android SDK API 34
- Android Gradle Plugin 8.5.2

### iOS 开发
- macOS 12.0 或更高版本
- Xcode 14.0 或更高版本
- iOS 14.0 或更高版本

### 鸿蒙 Next 开发
- DevEco Studio 5.0 或更高版本
- HarmonyOS NEXT SDK

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-username/HomeworkAssistantKMP.git
cd HomeworkAssistantKMP
```

### 2. 构建项目

#### Android
```bash
./gradlew :composeApp:assembleDebug
```

#### iOS
```bash
./gradlew :composeApp:iosSimulatorArm64MainBinaries
```

#### 鸿蒙 Next
```bash
./gradlew :shared:linkDebugSharedOhosArm64
```

### 3. 运行应用

#### Android
在 Android Studio 中打开项目，选择 `composeApp` 配置并运行。

#### iOS
1. 在 Xcode 中打开 `iosApp/iosApp.xcodeproj`
2. 选择目标设备或模拟器
3. 点击运行按钮

#### 鸿蒙 Next
1. 在 DevEco Studio 中打开 `harmonyApp` 项目
2. 构建并运行到鸿蒙设备或模拟器

## 开发指南

### TDD 开发流程
本项目采用测试驱动开发（TDD）方法：

1. **Red 阶段**: 编写失败的测试用例
2. **Green 阶段**: 编写最少代码使测试通过
3. **Refactor 阶段**: 重构代码保持测试通过

### Git 提交规范
- 🔴 `RED: 添加测试 - [测试描述]`
- 🟢 `GREEN: 实现功能 - [功能描述]`
- 🔵 `REFACTOR: 优化代码 - [重构内容]`
- 📝 `FEATURE: 完成特性 - [特性名称]`

## 许可证

本项目采用 Apache 2.0 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request！请确保：

1. 遵循项目的代码风格
2. 编写相应的测试用例
3. 更新相关文档
4. 提交前运行所有测试

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](https://github.com/your-username/HomeworkAssistantKMP/issues)
- 发送邮件至：<EMAIL>

---

基于 [OvCompose](https://github.com/Tencent-TDS/ovCompose-multiplatform-core) 跨平台框架构建

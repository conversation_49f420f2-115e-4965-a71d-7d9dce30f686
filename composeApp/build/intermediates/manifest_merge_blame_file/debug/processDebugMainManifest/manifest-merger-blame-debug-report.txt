1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.homework.assistant"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:5-67
12-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:5-79
13-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:22-76
14
15    <!-- 音频权限 -->
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:5-71
16-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:22-68
17    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
17-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:5-80
17-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:22-77
18
19    <!-- 相机权限 -->
20    <uses-permission android:name="android.permission.CAMERA" />
20-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:5-65
20-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:22-62
21
22    <!-- 存储权限 -->
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:5-80
23-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:5-81
24-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:22-78
25
26    <!-- 相机特性 -->
27    <uses-feature
27-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:20:5-22:36
28        android:name="android.hardware.camera"
28-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:21:9-47
29        android:required="false" />
29-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:22:9-33
30    <uses-feature
30-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:23:5-25:36
31        android:name="android.hardware.camera.autofocus"
31-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:24:9-57
32        android:required="false" />
32-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:25:9-33
33
34    <permission
34-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:22:5-24:47
35        android:name="com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:22-94
39
40    <application
40-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:27:5-41:19
41        android:allowBackup="true"
41-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:28:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:label="@string/app_name"
45-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:29:9-41
46        android:supportsRtl="true" >
46-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:30:9-35
47        <activity
47-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:32:9-40:20
48            android:name="com.homework.assistant.MainActivity"
48-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:33:13-41
49            android:exported="true"
49-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:34:13-36
50            android:launchMode="singleTop" >
50-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:35:13-43
51            <intent-filter>
51-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:36:13-39:29
52                <action android:name="android.intent.action.MAIN" />
52-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:17-69
52-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:17-77
54-->/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->[androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
58            android:name="androidx.compose.ui.tooling.PreviewActivity"
58-->[androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
59            android:exported="true" />
59-->[androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
60
61        <provider
61-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
62            android:name="androidx.startup.InitializationProvider"
62-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
63            android:authorities="com.homework.assistant.androidx-startup"
63-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
64            android:exported="false" >
64-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
74        </provider>
75
76        <receiver
76-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
77            android:name="androidx.profileinstaller.ProfileInstallReceiver"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
78            android:directBootAware="false"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
79            android:enabled="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
80            android:exported="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
81            android:permission="android.permission.DUMP" >
81-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
83                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
83-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
86                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
86-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
89                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
89-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
92                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
93            </intent-filter>
94        </receiver>
95    </application>
96
97</manifest>

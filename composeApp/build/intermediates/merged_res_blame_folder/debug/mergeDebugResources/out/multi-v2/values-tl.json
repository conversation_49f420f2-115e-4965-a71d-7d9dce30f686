{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-41:/values-tl/values-tl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4800,4889,4996,5076,5160,5260,5364,5464,5570,5658,5770,5875,5985,6104,6184,6291", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4795,4884,4991,5071,5155,5255,5359,5459,5565,5653,5765,5870,5980,6099,6179,6286,6381"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1487,1611,1733,1851,1972,2071,2171,2288,2435,2562,2712,2797,2896,2991,3089,3210,3348,3452,3599,3747,3894,4064,4202,4325,4450,4575,4671,4770,4895,5030,5137,5241,5354,5499,5648,5764,5870,5946,6046,6143,6232,6321,6428,6508,6592,6692,6796,6896,7002,7090,7202,7307,7417,7536,7616,7723", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "1606,1728,1846,1967,2066,2166,2283,2430,2557,2707,2792,2891,2986,3084,3205,3343,3447,3594,3742,3889,4059,4197,4320,4445,4570,4666,4765,4890,5025,5132,5236,5349,5494,5643,5759,5865,5941,6041,6138,6227,6316,6423,6503,6587,6687,6791,6891,6997,7085,7197,7302,7412,7531,7611,7718,7813"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,938,1024,1121,1223,1313,1395,7818,7910,7994,8064,8133,8220,8306,8478,8556,8622", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "933,1019,1116,1218,1308,1390,1482,7905,7989,8059,8128,8215,8301,8372,8551,8617,8744"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,405,502,609,717,8377", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "197,299,400,497,604,712,834,8473"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/res/values-tl/values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8749,8835", "endColumns": "85,91", "endOffsets": "8830,8922"}}]}]}
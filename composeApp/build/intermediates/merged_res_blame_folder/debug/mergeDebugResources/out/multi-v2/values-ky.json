{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-41:/values-ky/values-ky.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,103", "endOffsets": "149,253"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8667,8766", "endColumns": "98,103", "endOffsets": "8761,8865"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4690,4775,4887,4976,5060,5160,5261,5357,5454,5541,5652,5751,5851,5999,6089,6208", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4685,4770,4882,4971,5055,5155,5256,5352,5449,5536,5647,5746,5846,5994,6084,6203,6311"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1488,1611,1728,1842,1967,2067,2165,2280,2416,2557,2713,2797,2895,2987,3084,3200,3319,3422,3558,3692,3829,4004,4133,4250,4370,4491,4584,4682,4804,4941,5044,5169,5274,5408,5547,5656,5758,5834,5933,6037,6123,6208,6320,6409,6493,6593,6694,6790,6887,6974,7085,7184,7284,7432,7522,7641", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "1606,1723,1837,1962,2062,2160,2275,2411,2552,2708,2792,2890,2982,3079,3195,3314,3417,3553,3687,3824,3999,4128,4245,4365,4486,4579,4677,4799,4936,5039,5164,5269,5403,5542,5651,5753,5829,5928,6032,6118,6203,6315,6404,6488,6588,6689,6785,6882,6969,7080,7179,7279,7427,7517,7636,7744"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1013,1123,1223,1308,1390,7749,7838,7923,7989,8056,8141,8228,8402,8481,8549", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "924,1008,1118,1218,1303,1385,1483,7833,7918,7984,8051,8136,8223,8296,8476,8544,8662"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,621,725,8301", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "200,302,405,512,616,720,831,8397"}}]}]}
{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-41:/values-da/values-da.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/res/values-da/values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8381,8471", "endColumns": "89,86", "endOffsets": "8466,8553"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,400,498,605,714,8015", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "196,298,395,493,600,709,827,8111"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4546,4631,4732,4812,4896,4997,5096,5191,5291,5378,5483,5585,5690,5807,5887,5989", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4541,4626,4727,4807,4891,4992,5091,5186,5286,5373,5478,5580,5685,5802,5882,5984,6083"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1446,1562,1675,1782,1896,1996,2091,2203,2347,2469,2618,2702,2802,2891,2985,3099,3217,3322,3447,3567,3703,3876,4006,4123,4245,4364,4454,4552,4671,4807,4905,5023,5125,5251,5384,5489,5587,5667,5760,5853,5937,6022,6123,6203,6287,6388,6487,6582,6682,6769,6874,6976,7081,7198,7278,7380", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "1557,1670,1777,1891,1991,2086,2198,2342,2464,2613,2697,2797,2886,2980,3094,3212,3317,3442,3562,3698,3871,4001,4118,4240,4359,4449,4547,4666,4802,4900,5018,5120,5246,5379,5484,5582,5662,5755,5848,5932,6017,6118,6198,6282,6383,6482,6577,6677,6764,6869,6971,7076,7193,7273,7375,7474"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,924,1004,1099,1198,1280,1357,7479,7568,7650,7715,7780,7861,7945,8116,8194,8261", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "919,999,1094,1193,1275,1352,1441,7563,7645,7710,7775,7856,7940,8010,8189,8256,8376"}}]}]}
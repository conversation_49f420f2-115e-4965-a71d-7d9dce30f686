{"logs": [{"outputFile": "com.homework.assistant.composeApp-mergeDebugResources-41:/values-sl/values-sl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4703,4789,4892,4974,5057,5152,5252,5343,5440,5528,5632,5729,5831,5973,6055,6161", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4698,4784,4887,4969,5052,5147,5247,5338,5435,5523,5627,5724,5826,5968,6050,6156,6255"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1447,1576,1705,1823,1952,2062,2158,2271,2411,2537,2680,2765,2864,2957,3054,3171,3293,3397,3534,3668,3799,3983,4110,4233,4358,4480,4574,4672,4792,4916,5016,5125,5231,5374,5521,5630,5732,5816,5911,6007,6095,6181,6284,6366,6449,6544,6644,6735,6832,6920,7024,7121,7223,7365,7447,7553", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "1571,1700,1818,1947,2057,2153,2266,2406,2532,2675,2760,2859,2952,3049,3166,3288,3392,3529,3663,3794,3978,4105,4228,4353,4475,4569,4667,4787,4911,5011,5120,5226,5369,5516,5625,5727,5811,5906,6002,6090,6176,6279,6361,6444,6539,6639,6730,6827,6915,7019,7116,7218,7360,7442,7548,7647"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,923,1004,1100,1198,1283,1360,7652,7744,7826,7899,7971,8053,8139,8312,8390,8460", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "918,999,1095,1193,1278,1355,1442,7739,7821,7894,7966,8048,8134,8206,8385,8455,8576"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,506,609,711,8211", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "197,299,397,501,604,706,823,8307"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/res/values-sl/values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8581,8671", "endColumns": "89,90", "endOffsets": "8666,8757"}}]}]}
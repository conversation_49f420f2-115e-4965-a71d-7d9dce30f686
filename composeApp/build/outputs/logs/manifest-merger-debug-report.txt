-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:1-43:12
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/6be31af3ffca920956b329097870fa08/transformed/activity-1.9.0/AndroidManifest.xml:17:1-22:12
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/50c6710077c488e48e03b0f42b2b4ed7/transformed/library-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ecb6ef2fb8729b9d3059395b76266a5a/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/0fd6fd44660ed1a1f44c9840ced05aca/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ab34a2e3897cc3b5df897ca0b7c6e5b8/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/1f455eea3700eb76fd347fccad2c1ee9/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8f8fa6f89f8325c3c2dd787359c80334/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b514871e207c178fb3875fc74e67fa28/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/0e11e64d8b6b0b40d8fc9fcc64130547/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/94771e61618b3435cc6c26dbceb3db01/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/fb0a9db49011e2cc9a517704807604cc/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7a8b5efe07601304df714536c2e705e0/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7657f8a869cc90b575d1e26b1aefcfb9/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/806dde83022591ca4a5ae794405f6c80/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/9a1ec39b15ba2d46b600877777400b17/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/249834dc8b5e139befa0d76ee82a5b6e/transformed/activity-ktx-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/d56f84438d1c6ea99581faf83cca0f67/transformed/activity-compose-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/5e58d8e264279a3cb9e6bfbeadd6edbf/transformed/library-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/245f9c764aef503a856d72883206528d/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/f7eb779fa40073324d20acb1acb6e349/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/bf51ed41ec4011ee50d741db25941e64/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/00c98dde4513084bc7fc2eb4647423ef/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/6817f7dd3f76aa1467a9d0493a7c4dbd/transformed/core-ktx-1.13.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/9a85f144a8d51a87da460022e77ef72a/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/0ad9ab6810364a8772f5898c245f3785/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/4bbab1e1abd26fa1290c06f9a4711a3b/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/9222dbe0ed73e4a3a1115e5f0ab715bf/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/3d5c4654eeca6bd152e42b400e3d2b59/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/bda1920578cb77f959213961ac1bc9d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c1ccf714a0a04cf5e185f3937f1ab63a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/7014f995ecff9344c1f8a3dc716f8f84/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/2d4db3775d9ae8a1e6812ae2a46c6084/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/3e6e71e9c9f2e734b30948f43dfc49b3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/2c7307afcd0c57b67e206dba22a42320/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/fcd1b80de7ad7a5703928dca3d3d9a08/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/47947a315a7bb3af2d7abf2052b69722/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:5-67
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:5-79
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:5-71
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:9:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:10:22-77
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:5-65
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:13:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:5-80
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:16:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:5-81
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:17:22-78
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:22:9-33
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:21:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:25:9-33
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:24:9-57
application
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:27:5-41:19
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:27:5-41:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/3e6e71e9c9f2e734b30948f43dfc49b3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/3e6e71e9c9f2e734b30948f43dfc49b3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:30:9-35
	android:label
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:29:9-41
	android:allowBackup
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:28:9-35
activity#com.homework.assistant.MainActivity
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:32:9-40:20
	android:launchMode
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:35:13-43
	android:exported
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:34:13-36
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:33:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:36:13-39:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:17-69
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:37:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:17-77
	android:name
		ADDED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml:38:27-74
uses-sdk
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared] /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/6be31af3ffca920956b329097870fa08/transformed/activity-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/6be31af3ffca920956b329097870fa08/transformed/activity-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/50c6710077c488e48e03b0f42b2b4ed7/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-resources-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/50c6710077c488e48e03b0f42b2b4ed7/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/82e833e2effa362bf0becd81a3321826/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ecb6ef2fb8729b9d3059395b76266a5a/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ecb6ef2fb8729b9d3059395b76266a5a/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/0fd6fd44660ed1a1f44c9840ced05aca/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/0fd6fd44660ed1a1f44c9840ced05aca/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ab34a2e3897cc3b5df897ca0b7c6e5b8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] /Users/<USER>/.gradle/caches/transforms-4/ab34a2e3897cc3b5df897ca0b7c6e5b8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/1f455eea3700eb76fd347fccad2c1ee9/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/1f455eea3700eb76fd347fccad2c1ee9/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8f8fa6f89f8325c3c2dd787359c80334/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/8f8fa6f89f8325c3c2dd787359c80334/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b514871e207c178fb3875fc74e67fa28/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/b514871e207c178fb3875fc74e67fa28/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/048a6d982f9aa1e4dfff21b73b777e16/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/0e11e64d8b6b0b40d8fc9fcc64130547/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/0e11e64d8b6b0b40d8fc9fcc64130547/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/94771e61618b3435cc6c26dbceb3db01/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/94771e61618b3435cc6c26dbceb3db01/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/fb0a9db49011e2cc9a517704807604cc/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/fb0a9db49011e2cc9a517704807604cc/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7a8b5efe07601304df714536c2e705e0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7a8b5efe07601304df714536c2e705e0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7657f8a869cc90b575d1e26b1aefcfb9/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/7657f8a869cc90b575d1e26b1aefcfb9/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/806dde83022591ca4a5ae794405f6c80/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/806dde83022591ca4a5ae794405f6c80/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/9a1ec39b15ba2d46b600877777400b17/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/9a1ec39b15ba2d46b600877777400b17/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/97d311d9480daceacc94ef7fd0d9e58d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/249834dc8b5e139befa0d76ee82a5b6e/transformed/activity-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/249834dc8b5e139befa0d76ee82a5b6e/transformed/activity-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/d56f84438d1c6ea99581faf83cca0f67/transformed/activity-compose-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] /Users/<USER>/.gradle/caches/transforms-4/d56f84438d1c6ea99581faf83cca0f67/transformed/activity-compose-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/5e58d8e264279a3cb9e6bfbeadd6edbf/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.components:components-ui-tooling-preview-android:1.6.11] /Users/<USER>/.gradle/caches/transforms-4/5e58d8e264279a3cb9e6bfbeadd6edbf/transformed/library-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/245f9c764aef503a856d72883206528d/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/245f9c764aef503a856d72883206528d/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/f7eb779fa40073324d20acb1acb6e349/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/f7eb779fa40073324d20acb1acb6e349/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/bf51ed41ec4011ee50d741db25941e64/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/bf51ed41ec4011ee50d741db25941e64/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/00c98dde4513084bc7fc2eb4647423ef/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/00c98dde4513084bc7fc2eb4647423ef/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/6817f7dd3f76aa1467a9d0493a7c4dbd/transformed/core-ktx-1.13.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/6817f7dd3f76aa1467a9d0493a7c4dbd/transformed/core-ktx-1.13.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/9a85f144a8d51a87da460022e77ef72a/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/9a85f144a8d51a87da460022e77ef72a/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/0ad9ab6810364a8772f5898c245f3785/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/0ad9ab6810364a8772f5898c245f3785/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/4bbab1e1abd26fa1290c06f9a4711a3b/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/4bbab1e1abd26fa1290c06f9a4711a3b/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/9222dbe0ed73e4a3a1115e5f0ab715bf/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/9222dbe0ed73e4a3a1115e5f0ab715bf/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/3d5c4654eeca6bd152e42b400e3d2b59/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/3d5c4654eeca6bd152e42b400e3d2b59/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/bda1920578cb77f959213961ac1bc9d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/bda1920578cb77f959213961ac1bc9d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c1ccf714a0a04cf5e185f3937f1ab63a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c1ccf714a0a04cf5e185f3937f1ab63a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/7014f995ecff9344c1f8a3dc716f8f84/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-4/7014f995ecff9344c1f8a3dc716f8f84/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/2d4db3775d9ae8a1e6812ae2a46c6084/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-4/2d4db3775d9ae8a1e6812ae2a46c6084/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/3e6e71e9c9f2e734b30948f43dfc49b3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/3e6e71e9c9f2e734b30948f43dfc49b3/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/2c7307afcd0c57b67e206dba22a42320/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/2c7307afcd0c57b67e206dba22a42320/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/fcd1b80de7ad7a5703928dca3d3d9a08/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-4/fcd1b80de7ad7a5703928dca3d3d9a08/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/47947a315a7bb3af2d7abf2052b69722/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-4/47947a315a7bb3af2d7abf2052b69722/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/code/MyWorks/HomeworkAssistantKMP/composeApp/src/androidMain/AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] /Users/<USER>/.gradle/caches/transforms-4/042d59fe99cecb95403c44623eb4458e/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-4/17a2f2121f401beb1c1f7b0270f74104/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-4/0d2ef4010c8b08e47e6ea438eb1ab11b/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:23:9-81
permission#com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:22-94
uses-permission#com.homework.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] /Users/<USER>/.gradle/caches/transforms-4/93ee6e3290c19dab66a62a361a7b2498/transformed/core-1.13.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-4/c43fe763d68e65a905404b3e39467615/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-4/1498c506cc87d8038acee444e6d33262/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92

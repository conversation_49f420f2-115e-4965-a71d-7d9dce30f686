  App android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  App android.content.Context  
setContent android.content.Context  App android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  App  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  App #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  App -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  	Alignment .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  
FontWeight androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Preview androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  
cardElevation androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  height androidx.compose.material3  padding androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  
FontWeight androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  Preview androidx.compose.runtime  Spacer androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  
cardElevation androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  height androidx.compose.runtime  padding androidx.compose.runtime  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  App #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	Alignment com.homework.assistant  App com.homework.assistant  AppAndroidPreview com.homework.assistant  Arrangement com.homework.assistant  Bundle com.homework.assistant  Card com.homework.assistant  CardDefaults com.homework.assistant  Column com.homework.assistant  ComponentActivity com.homework.assistant  
Composable com.homework.assistant  
FontWeight com.homework.assistant  MainActivity com.homework.assistant  
MaterialTheme com.homework.assistant  Modifier com.homework.assistant  Preview com.homework.assistant  Spacer com.homework.assistant  Surface com.homework.assistant  Text com.homework.assistant  
cardElevation com.homework.assistant  fillMaxSize com.homework.assistant  fillMaxWidth com.homework.assistant  height com.homework.assistant  padding com.homework.assistant  App #com.homework.assistant.MainActivity  
setContent #com.homework.assistant.MainActivity  	ByteArray 3homeworkassistantkmp.composeapp.generated.resources  ExperimentalResourceApi 3homeworkassistantkmp.composeapp.generated.resources  OptIn 3homeworkassistantkmp.composeapp.generated.resources  String 3homeworkassistantkmp.composeapp.generated.resources  getResourceUri 3homeworkassistantkmp.composeapp.generated.resources  org 3homeworkassistantkmp.composeapp.generated.resources  readResourceBytes 3homeworkassistantkmp.composeapp.generated.resources  	ByteArray 7homeworkassistantkmp.composeapp.generated.resources.Res  ExperimentalResourceApi 7homeworkassistantkmp.composeapp.generated.resources.Res  String 7homeworkassistantkmp.composeapp.generated.resources.Res  getResourceUri 7homeworkassistantkmp.composeapp.generated.resources.Res  readResourceBytes 7homeworkassistantkmp.composeapp.generated.resources.Res  	ByteArray kotlin  OptIn kotlin  String kotlin  plus 
kotlin.String  ExperimentalResourceApi org.jetbrains.compose.resources  InternalResourceApi org.jetbrains.compose.resources  getResourceUri org.jetbrains.compose.resources  readResourceBytes org.jetbrains.compose.resources  Preview (org.jetbrains.compose.ui.tooling.preview                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
{"projectStructure": {"formatVersion": "0.3.3", "isPublishedAsRoot": "true", "variants": [{"name": "iosArm64ApiElements", "sourceSet": ["iosMain", "appleMain", "nativeMain", "commonMain"]}, {"name": "iosSimulatorArm64ApiElements", "sourceSet": ["iosMain", "appleMain", "nativeMain", "commonMain"]}, {"name": "iosX64ApiElements", "sourceSet": ["iosMain", "appleMain", "nativeMain", "commonMain"]}], "sourceSets": [{"name": "appleMain", "dependsOn": ["nativeMain"], "moduleDependency": ["org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.compose.runtime:runtime", "org.jetbrains.compose.foundation:foundation", "org.jetbrains.compose.material3:material3", "org.jetbrains.compose.ui:ui", "org.jetbrains.compose.components:components-resources", "org.jetbrains.compose.components:components-ui-tooling-preview", "HomeworkAssistantKMP:shared"], "sourceSetCInteropMetadataDirectory": "appleMain-cinterop", "binaryLayout": "klib", "hostSpecific": "true"}, {"name": "commonMain", "dependsOn": [], "moduleDependency": ["org.jetbrains.kotlin:kotlin-stdlib"], "binaryLayout": "klib"}, {"name": "iosMain", "dependsOn": ["appleMain"], "moduleDependency": ["org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.compose.runtime:runtime", "org.jetbrains.compose.foundation:foundation", "org.jetbrains.compose.material3:material3", "org.jetbrains.compose.ui:ui", "org.jetbrains.compose.components:components-resources", "org.jetbrains.compose.components:components-ui-tooling-preview", "HomeworkAssistantKMP:shared"], "sourceSetCInteropMetadataDirectory": "iosMain-cinterop", "binaryLayout": "klib", "hostSpecific": "true"}, {"name": "nativeMain", "dependsOn": ["commonMain"], "moduleDependency": ["org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.compose.runtime:runtime", "org.jetbrains.compose.foundation:foundation", "org.jetbrains.compose.material3:material3", "org.jetbrains.compose.ui:ui", "org.jetbrains.compose.components:components-resources", "org.jetbrains.compose.components:components-ui-tooling-preview", "HomeworkAssistantKMP:shared"], "sourceSetCInteropMetadataDirectory": "nativeMain-cinterop", "binaryLayout": "klib", "hostSpecific": "true"}]}}
  SDK_INT android.os.Build.VERSION  AndroidPlatform com.homework.assistant.shared  Platform com.homework.assistant.shared  String com.homework.assistant.shared  android com.homework.assistant.shared  getPlatform com.homework.assistant.shared  android -com.homework.assistant.shared.AndroidPlatform  Flow (com.homework.assistant.shared.data.local  Int (com.homework.assistant.shared.data.local  List (com.homework.assistant.shared.data.local  Long (com.homework.assistant.shared.data.local  
Recitation (com.homework.assistant.shared.data.local  RecitationCategory (com.homework.assistant.shared.data.local  RecitationType (com.homework.assistant.shared.data.local  String (com.homework.assistant.shared.data.local  StudySession (com.homework.assistant.shared.data.local  	StudyType (com.homework.assistant.shared.data.local  Word (com.homework.assistant.shared.data.local  WordCategory (com.homework.assistant.shared.data.local  AppSettings (com.homework.assistant.shared.data.model  Boolean (com.homework.assistant.shared.data.model  
DailyProgress (com.homework.assistant.shared.data.model  DictationSettings (com.homework.assistant.shared.data.model  Double (com.homework.assistant.shared.data.model  Float (com.homework.assistant.shared.data.model  Flow (com.homework.assistant.shared.data.model  GeneralSettings (com.homework.assistant.shared.data.model  Int (com.homework.assistant.shared.data.model  List (com.homework.assistant.shared.data.model  Long (com.homework.assistant.shared.data.model  PrivacySettings (com.homework.assistant.shared.data.model  
Recitation (com.homework.assistant.shared.data.model  RecitationCategory (com.homework.assistant.shared.data.model  RecitationSettings (com.homework.assistant.shared.data.model  RecitationType (com.homework.assistant.shared.data.model  Result (com.homework.assistant.shared.data.model  Serializable (com.homework.assistant.shared.data.model  String (com.homework.assistant.shared.data.model  StudySession (com.homework.assistant.shared.data.model  StudyStatistics (com.homework.assistant.shared.data.model  StudyStatus (com.homework.assistant.shared.data.model  	StudyType (com.homework.assistant.shared.data.model  StudyTypeStats (com.homework.assistant.shared.data.model  System (com.homework.assistant.shared.data.model  
UISettings (com.homework.assistant.shared.data.model  Unit (com.homework.assistant.shared.data.model  
VoiceSettings (com.homework.assistant.shared.data.model  Word (com.homework.assistant.shared.data.model  WordCategory (com.homework.assistant.shared.data.model  	emptyList (com.homework.assistant.shared.data.model  DictationSettings 4com.homework.assistant.shared.data.model.AppSettings  GeneralSettings 4com.homework.assistant.shared.data.model.AppSettings  Long 4com.homework.assistant.shared.data.model.AppSettings  PrivacySettings 4com.homework.assistant.shared.data.model.AppSettings  RecitationSettings 4com.homework.assistant.shared.data.model.AppSettings  System 4com.homework.assistant.shared.data.model.AppSettings  
UISettings 4com.homework.assistant.shared.data.model.AppSettings  
VoiceSettings 4com.homework.assistant.shared.data.model.AppSettings  DictationSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  GeneralSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  PrivacySettings >com.homework.assistant.shared.data.model.AppSettings.Companion  RecitationSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  System >com.homework.assistant.shared.data.model.AppSettings.Companion  
UISettings >com.homework.assistant.shared.data.model.AppSettings.Companion  
VoiceSettings >com.homework.assistant.shared.data.model.AppSettings.Companion  Double 6com.homework.assistant.shared.data.model.DailyProgress  Int 6com.homework.assistant.shared.data.model.DailyProgress  Long 6com.homework.assistant.shared.data.model.DailyProgress  String 6com.homework.assistant.shared.data.model.DailyProgress  Boolean :com.homework.assistant.shared.data.model.DictationSettings  Float :com.homework.assistant.shared.data.model.DictationSettings  Int :com.homework.assistant.shared.data.model.DictationSettings  Long :com.homework.assistant.shared.data.model.DictationSettings  Boolean 8com.homework.assistant.shared.data.model.GeneralSettings  String 8com.homework.assistant.shared.data.model.GeneralSettings  Boolean 8com.homework.assistant.shared.data.model.PrivacySettings  Int 3com.homework.assistant.shared.data.model.Recitation  List 3com.homework.assistant.shared.data.model.Recitation  Long 3com.homework.assistant.shared.data.model.Recitation  String 3com.homework.assistant.shared.data.model.Recitation  System 3com.homework.assistant.shared.data.model.Recitation  	emptyList 3com.homework.assistant.shared.data.model.Recitation  System =com.homework.assistant.shared.data.model.Recitation.Companion  	emptyList =com.homework.assistant.shared.data.model.Recitation.Companion  Int ;com.homework.assistant.shared.data.model.RecitationCategory  Long ;com.homework.assistant.shared.data.model.RecitationCategory  RecitationType ;com.homework.assistant.shared.data.model.RecitationCategory  String ;com.homework.assistant.shared.data.model.RecitationCategory  System ;com.homework.assistant.shared.data.model.RecitationCategory  RecitationType Ecom.homework.assistant.shared.data.model.RecitationCategory.Companion  System Ecom.homework.assistant.shared.data.model.RecitationCategory.Companion  Boolean ;com.homework.assistant.shared.data.model.RecitationSettings  Long ;com.homework.assistant.shared.data.model.RecitationSettings  	Companion 7com.homework.assistant.shared.data.model.RecitationType  POEM 7com.homework.assistant.shared.data.model.RecitationType  Int 5com.homework.assistant.shared.data.model.StudySession  List 5com.homework.assistant.shared.data.model.StudySession  Long 5com.homework.assistant.shared.data.model.StudySession  String 5com.homework.assistant.shared.data.model.StudySession  StudyStatus 5com.homework.assistant.shared.data.model.StudySession  	StudyType 5com.homework.assistant.shared.data.model.StudySession  System 5com.homework.assistant.shared.data.model.StudySession  	emptyList 5com.homework.assistant.shared.data.model.StudySession  StudyStatus ?com.homework.assistant.shared.data.model.StudySession.Companion  System ?com.homework.assistant.shared.data.model.StudySession.Companion  	emptyList ?com.homework.assistant.shared.data.model.StudySession.Companion  
DailyProgress 8com.homework.assistant.shared.data.model.StudyStatistics  Double 8com.homework.assistant.shared.data.model.StudyStatistics  Int 8com.homework.assistant.shared.data.model.StudyStatistics  List 8com.homework.assistant.shared.data.model.StudyStatistics  Long 8com.homework.assistant.shared.data.model.StudyStatistics  StudyTypeStats 8com.homework.assistant.shared.data.model.StudyStatistics  	emptyList 8com.homework.assistant.shared.data.model.StudyStatistics  StudyTypeStats Bcom.homework.assistant.shared.data.model.StudyStatistics.Companion  	emptyList Bcom.homework.assistant.shared.data.model.StudyStatistics.Companion  	Companion 4com.homework.assistant.shared.data.model.StudyStatus  IN_PROGRESS 4com.homework.assistant.shared.data.model.StudyStatus  Double 7com.homework.assistant.shared.data.model.StudyTypeStats  Int 7com.homework.assistant.shared.data.model.StudyTypeStats  Long 7com.homework.assistant.shared.data.model.StudyTypeStats  Boolean 3com.homework.assistant.shared.data.model.UISettings  Float 3com.homework.assistant.shared.data.model.UISettings  String 3com.homework.assistant.shared.data.model.UISettings  Boolean 6com.homework.assistant.shared.data.model.VoiceSettings  Float 6com.homework.assistant.shared.data.model.VoiceSettings  Long 6com.homework.assistant.shared.data.model.VoiceSettings  String 6com.homework.assistant.shared.data.model.VoiceSettings  Int -com.homework.assistant.shared.data.model.Word  List -com.homework.assistant.shared.data.model.Word  Long -com.homework.assistant.shared.data.model.Word  String -com.homework.assistant.shared.data.model.Word  System -com.homework.assistant.shared.data.model.Word  	emptyList -com.homework.assistant.shared.data.model.Word  System 7com.homework.assistant.shared.data.model.Word.Companion  	emptyList 7com.homework.assistant.shared.data.model.Word.Companion  Int 5com.homework.assistant.shared.data.model.WordCategory  Long 5com.homework.assistant.shared.data.model.WordCategory  String 5com.homework.assistant.shared.data.model.WordCategory  System 5com.homework.assistant.shared.data.model.WordCategory  System ?com.homework.assistant.shared.data.model.WordCategory.Companion  AppSettings -com.homework.assistant.shared.data.repository  
DailyProgress -com.homework.assistant.shared.data.repository  DictationSettings -com.homework.assistant.shared.data.repository  Double -com.homework.assistant.shared.data.repository  Flow -com.homework.assistant.shared.data.repository  GeneralSettings -com.homework.assistant.shared.data.repository  Int -com.homework.assistant.shared.data.repository  List -com.homework.assistant.shared.data.repository  Long -com.homework.assistant.shared.data.repository  PrivacySettings -com.homework.assistant.shared.data.repository  
Recitation -com.homework.assistant.shared.data.repository  RecitationCategory -com.homework.assistant.shared.data.repository  RecitationSettings -com.homework.assistant.shared.data.repository  RecitationType -com.homework.assistant.shared.data.repository  Result -com.homework.assistant.shared.data.repository  String -com.homework.assistant.shared.data.repository  StudySession -com.homework.assistant.shared.data.repository  StudyStatistics -com.homework.assistant.shared.data.repository  	StudyType -com.homework.assistant.shared.data.repository  
UISettings -com.homework.assistant.shared.data.repository  Unit -com.homework.assistant.shared.data.repository  
VoiceSettings -com.homework.assistant.shared.data.repository  Word -com.homework.assistant.shared.data.repository  WordCategory -com.homework.assistant.shared.data.repository  Boolean #com.homework.assistant.shared.utils  Clock #com.homework.assistant.shared.utils  DateTimePeriod #com.homework.assistant.shared.utils  Instant #com.homework.assistant.shared.utils  Int #com.homework.assistant.shared.utils  Long #com.homework.assistant.shared.utils  Random #com.homework.assistant.shared.utils  String #com.homework.assistant.shared.utils  TimeZone #com.homework.assistant.shared.utils  atTime #com.homework.assistant.shared.utils  currentSystemDefault #com.homework.assistant.shared.utils  	daysUntil #com.homework.assistant.shared.utils  fromEpochMilliseconds #com.homework.assistant.shared.utils  joinToString #com.homework.assistant.shared.utils  map #com.homework.assistant.shared.utils  minus #com.homework.assistant.shared.utils  nextInt #com.homework.assistant.shared.utils  now #com.homework.assistant.shared.utils  padStart #com.homework.assistant.shared.utils  	toInstant #com.homework.assistant.shared.utils  toLocalDateTime #com.homework.assistant.shared.utils  Clock -com.homework.assistant.shared.utils.DateUtils  DateTimePeriod -com.homework.assistant.shared.utils.DateUtils  Instant -com.homework.assistant.shared.utils.DateUtils  TimeZone -com.homework.assistant.shared.utils.DateUtils  atTime -com.homework.assistant.shared.utils.DateUtils  currentSystemDefault -com.homework.assistant.shared.utils.DateUtils  	daysUntil -com.homework.assistant.shared.utils.DateUtils  
formatDate -com.homework.assistant.shared.utils.DateUtils  
formatTime -com.homework.assistant.shared.utils.DateUtils  fromEpochMilliseconds -com.homework.assistant.shared.utils.DateUtils  minus -com.homework.assistant.shared.utils.DateUtils  now -com.homework.assistant.shared.utils.DateUtils  padStart -com.homework.assistant.shared.utils.DateUtils  	toInstant -com.homework.assistant.shared.utils.DateUtils  toLocalDateTime -com.homework.assistant.shared.utils.DateUtils  CHARS /com.homework.assistant.shared.utils.IdGenerator  Random /com.homework.assistant.shared.utils.IdGenerator  
generateId /com.homework.assistant.shared.utils.IdGenerator  generateIdWithPrefix /com.homework.assistant.shared.utils.IdGenerator  joinToString /com.homework.assistant.shared.utils.IdGenerator  map /com.homework.assistant.shared.utils.IdGenerator  nextInt /com.homework.assistant.shared.utils.IdGenerator  	ByteArray /homeworkassistantkmp.shared.generated.resources  ExperimentalResourceApi /homeworkassistantkmp.shared.generated.resources  OptIn /homeworkassistantkmp.shared.generated.resources  String /homeworkassistantkmp.shared.generated.resources  getResourceUri /homeworkassistantkmp.shared.generated.resources  org /homeworkassistantkmp.shared.generated.resources  readResourceBytes /homeworkassistantkmp.shared.generated.resources  	ByteArray 3homeworkassistantkmp.shared.generated.resources.Res  ExperimentalResourceApi 3homeworkassistantkmp.shared.generated.resources.Res  String 3homeworkassistantkmp.shared.generated.resources.Res  getResourceUri 3homeworkassistantkmp.shared.generated.resources.Res  readResourceBytes 3homeworkassistantkmp.shared.generated.resources.Res  currentTimeMillis java.lang.System  	ByteArray kotlin  CharSequence kotlin  	Function1 kotlin  OptIn kotlin  Result kotlin  String kotlin  map kotlin  rangeTo 
kotlin.Int  toString 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  rem kotlin.Long  get 
kotlin.String  length 
kotlin.String  padStart 
kotlin.String  plus 
kotlin.String  List kotlin.collections  	emptyList kotlin.collections  joinToString kotlin.collections  map kotlin.collections  joinToString kotlin.collections.List  Random 
kotlin.random  Default kotlin.random.Random  nextInt kotlin.random.Random  nextInt kotlin.random.Random.Default  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  map kotlin.ranges.IntRange  Sequence kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  map kotlin.text  padStart kotlin.text  Duration kotlin.time  Flow kotlinx.coroutines.flow  Boolean kotlinx.datetime  Clock kotlinx.datetime  
DatePeriod kotlinx.datetime  DateTimePeriod kotlinx.datetime  Instant kotlinx.datetime  Int kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  	LocalTime kotlinx.datetime  Long kotlinx.datetime  String kotlinx.datetime  TimeZone kotlinx.datetime  atTime kotlinx.datetime  currentSystemDefault kotlinx.datetime  	daysUntil kotlinx.datetime  fromEpochMilliseconds kotlinx.datetime  minus kotlinx.datetime  now kotlinx.datetime  padStart kotlinx.datetime  	toInstant kotlinx.datetime  toLocalDateTime kotlinx.datetime  	Companion kotlinx.datetime.Clock  System kotlinx.datetime.Clock  now kotlinx.datetime.Clock.System  	Companion kotlinx.datetime.Instant  fromEpochMilliseconds kotlinx.datetime.Instant  minus kotlinx.datetime.Instant  toEpochMilliseconds kotlinx.datetime.Instant  toLocalDateTime kotlinx.datetime.Instant  fromEpochMilliseconds "kotlinx.datetime.Instant.Companion  atTime kotlinx.datetime.LocalDate  	daysUntil kotlinx.datetime.LocalDate  toString kotlinx.datetime.LocalDate  date kotlinx.datetime.LocalDateTime  time kotlinx.datetime.LocalDateTime  	toInstant kotlinx.datetime.LocalDateTime  hour kotlinx.datetime.LocalTime  minute kotlinx.datetime.LocalTime  second kotlinx.datetime.LocalTime  	Companion kotlinx.datetime.TimeZone  currentSystemDefault kotlinx.datetime.TimeZone  currentSystemDefault #kotlinx.datetime.TimeZone.Companion  Serializable kotlinx.serialization  ExperimentalResourceApi org.jetbrains.compose.resources  InternalResourceApi org.jetbrains.compose.resources  getResourceUri org.jetbrains.compose.resources  readResourceBytes org.jetbrains.compose.resources                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
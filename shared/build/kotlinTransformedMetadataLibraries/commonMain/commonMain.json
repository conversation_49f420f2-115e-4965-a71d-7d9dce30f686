[{"moduleId": "module org.jetbrains.kotlin:kotlin-stdlib:2.0.21-KBA-005", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlin-kotlin-stdlib-2.0.21-KBA-005-commonMain-I9Ctmg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.runtime-runtime-1.6.1-KBA-002-commonMain-jbko9A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-1.6.1-KBA-002-commonMain-d1QLxg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material3:material3:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material3-material3-1.6.1-KBA-002-commonMain-mndCVQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-1.6.1-KBA-002-commonMain-gRqwuQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-resources-1.6.1-KBA-002-blockingMain-Bu3wWA.klib", "sourceSetName": "<PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.compose.components:components-resources:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-resources-1.6.1-KBA-002-commonMain-Bu3wWA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.components:components-ui-tooling-preview:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.components-components-ui-tooling-preview-1.6.1-KBA-002-commonMain-eIISOQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0-KBA-001", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.8.0-KBA-001-concurrentMain-0zsbkA.klib", "sourceSetName": "<PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0-KBA-001", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-coroutines-core-1.8.0-KBA-001-commonMain-0zsbkA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-serialization-json-1.6.3-commonMain-JDnEfA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-datetime:0.5.0", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-datetime-0.5.0-commonMain-iqDvsQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-client-core:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-client-core-2.3.8-commonMain-FU-9lg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-client-content-negotiation:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-client-content-negotiation-2.3.8-commonMain-jI37cw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-serialization-kotlinx-json:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-serialization-kotlinx-json-2.3.8-commonMain-sJ8SDA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-client-logging:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-client-logging-2.3.8-commonMain-grxlVw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-1.6.1-KBA-002-nativeMain-b7oo9A.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-1.6.1-KBA-002-jsNativeMain-b7oo9A.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-1.6.1-KBA-002-commonMain-b7oo9A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material:material-icons-core:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-icons-core-1.6.1-KBA-002-commonMain-va_j7A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.material:material-ripple:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-ripple-1.6.1-KBA-002-nativeMain-CJ42_A.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.material:material-ripple:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.material-material-ripple-1.6.1-KBA-002-commonMain-CJ42_A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-uikitMain-zUoLwg.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-nativeMain-t3mOtw.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-skikoExcludingWebMain-t3mOtw.klib", "sourceSetName": "skikoExcludingWebMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-jsNativeMain-t3mOtw.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-skikoMain-t3mOtw.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-graphics:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-graphics-1.6.1-KBA-002-commonMain-t3mOtw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-text-1.6.1-KBA-002-nativeMain-ksCnyQ.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-text-1.6.1-KBA-002-jsNativeMain-KwSShg.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-text-1.6.1-KBA-002-skikoMain-KwSShg.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-text:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-text-1.6.1-KBA-002-commonMain-KwSShg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.runtime:runtime-saveable:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.runtime-runtime-saveable-1.6.1-KBA-002-commonMain-VLqUWg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-geometry:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-geometry-1.6.1-KBA-002-commonMain-AefHCQ.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-unit-1.6.1-KBA-002-jsNativeMain-dFZ19Q.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-unit-1.6.1-KBA-002-jbMain-dFZ19Q.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-unit:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-unit-1.6.1-KBA-002-commonMain-dFZ19Q.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-util:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-util-1.6.1-KBA-002-jbMain-kxU9wA.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-util:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-util-1.6.1-KBA-002-commonMain-kxU9wA.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:kotlinx-serialization-core:1.6.3", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-kotlinx-serialization-core-1.6.3-commonMain-oyg_tw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-http:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-http-2.3.8-commonMain-QgEQ0Q.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-events:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-events-2.3.8-commonMain-_htHDg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-websocket-serialization:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-websocket-serialization-2.3.8-commonMain-8xBQEg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-serialization:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-serialization-2.3.8-commonMain-NxrIfg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-serialization-kotlinx:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-serialization-kotlinx-2.3.8-commonMain-s53Slg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-core-1.6.1-KBA-002-uikitMain-A0MamA.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-core-1.6.1-KBA-002-jsNativeMain-8wgXew.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-core-1.6.1-KBA-002-jbMain-8wgXew.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.animation:animation-core:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.animation-animation-core-1.6.1-KBA-002-commonMain-8wgXew.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-layout-1.6.1-KBA-002-uikitMain-AYR6xg.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-layout-1.6.1-KBA-002-skikoMain-tMli6A.klib", "sourceSetName": "skikoMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-layout-1.6.1-KBA-002-jsNativeMain-tMli6A.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.foundation:foundation-layout:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.foundation-foundation-layout-1.6.1-KBA-002-commonMain-tMli6A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.annotation-internal:annotation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.annotation-internal-annotation-1.6.1-KBA-002-nonJvmMain-c2Edvg.klib", "sourceSetName": "nonJvmMain"}, {"moduleId": "module org.jetbrains.compose.annotation-internal:annotation:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.annotation-internal-annotation-1.6.1-KBA-002-commonMain-c2Edvg.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.collection-internal:collection:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.collection-internal-collection-1.6.1-KBA-002-jsNativeMain-2rJm-g.klib", "sourceSetName": "jsNativeMain"}, {"moduleId": "module org.jetbrains.compose.collection-internal:collection:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.collection-internal-collection-1.6.1-KBA-002-jbMain-2rJm-g.klib", "sourceSetName": "jb<PERSON>ain"}, {"moduleId": "module org.jetbrains.compose.collection-internal:collection:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.collection-internal-collection-1.6.1-KBA-002-commonMain-2rJm-g.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.compose.ui:ui-uikit:1.6.1-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.compose.ui-ui-uikit-1.6.1-KBA-002-uikitMain-LtZ_ng.klib", "sourceSetName": "uikitMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.7.97-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.skiko-skiko-0.7.97-KBA-002-iosMain-OLJC0Q.klib", "sourceSetName": "iosMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.7.97-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.skiko-skiko-0.7.97-KBA-002-darwin<PERSON>ain-OLJC0Q.klib", "sourceSetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.7.97-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.skiko-skiko-0.7.97-KBA-002-nativeMain-1GUO7w.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.7.97-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.skiko-skiko-0.7.97-KBA-002-nativeJsMain-1GUO7w.klib", "sourceSetName": "nativeJsMain"}, {"moduleId": "module org.jetbrains.skiko:skiko:0.7.97-KBA-002", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.skiko-skiko-0.7.97-KBA-002-commonMain-1GUO7w.klib", "sourceSetName": "commonMain"}, {"moduleId": "module org.jetbrains.kotlinx:atomicfu:0.23.2-KBA-001", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-atomicfu-0.23.2-KBA-001-noOhosMain-RJQB-A.klib", "sourceSetName": "noOhosMain"}, {"moduleId": "module org.jetbrains.kotlinx:atomicfu:0.23.2-KBA-001", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-atomicfu-0.23.2-KBA-001-nativeMain-RJQB-A.klib", "sourceSetName": "nativeMain"}, {"moduleId": "module org.jetbrains.kotlinx:atomicfu:0.23.2-KBA-001", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/org.jetbrains.kotlinx-atomicfu-0.23.2-KBA-001-commonMain-RJQB-A.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-utils:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-utils-2.3.8-commonMain-jdPpcw.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-websockets:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-websockets-2.3.8-commonMain-8-9-_g.klib", "sourceSetName": "commonMain"}, {"moduleId": "module io.ktor:ktor-io:2.3.8", "file": "/Users/<USER>/code/MyWorks/HomeworkAssistantKMP/shared/build/kotlinTransformedMetadataLibraries/commonMain/io.ktor-ktor-io-2.3.8-commonMain-VFYQnA.klib", "sourceSetName": "commonMain"}]
package com.homework.assistant.shared.utils

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

/**
 * ID生成器测试
 */
class IdGeneratorTest {
    
    @Test
    fun `生成的ID应该有正确的长度`() {
        // Arrange & Act
        val id = IdGenerator.generateId()
        
        // Assert
        assertEquals(16, id.length)
    }
    
    @Test
    fun `生成的ID应该有指定的长度`() {
        // Arrange & Act
        val id = IdGenerator.generateId(20)
        
        // Assert
        assertEquals(20, id.length)
    }
    
    @Test
    fun `生成的ID应该只包含允许的字符`() {
        // Arrange
        val allowedChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        
        // Act
        val id = IdGenerator.generateId(100)
        
        // Assert
        id.forEach { char ->
            assertTrue(char in allowedChars, "字符 '$char' 不在允许的字符集中")
        }
    }
    
    @Test
    fun `连续生成的ID应该不相同`() {
        // Arrange & Act
        val id1 = IdGenerator.generateId()
        val id2 = IdGenerator.generateId()
        val id3 = IdGenerator.generateId()
        
        // Assert
        assertNotEquals(id1, id2)
        assertNotEquals(id2, id3)
        assertNotEquals(id1, id3)
    }
    
    @Test
    fun `带前缀的ID应该包含正确的前缀`() {
        // Arrange & Act
        val id = IdGenerator.generateIdWithPrefix("test", 10)
        
        // Assert
        assertTrue(id.startsWith("test_"))
        assertEquals(15, id.length) // "test_" + 10个字符
    }
    
    @Test
    fun `生字ID应该有正确的前缀`() {
        // Arrange & Act
        val id = IdGenerator.generateWordId()
        
        // Assert
        assertTrue(id.startsWith("word_"))
        assertEquals(17, id.length) // "word_" + 12个字符
    }
    
    @Test
    fun `背诵ID应该有正确的前缀`() {
        // Arrange & Act
        val id = IdGenerator.generateRecitationId()
        
        // Assert
        assertTrue(id.startsWith("recitation_"))
        assertEquals(23, id.length) // "recitation_" + 12个字符
    }
    
    @Test
    fun `学习会话ID应该有正确的前缀`() {
        // Arrange & Act
        val id = IdGenerator.generateSessionId()
        
        // Assert
        assertTrue(id.startsWith("session_"))
        assertEquals(20, id.length) // "session_" + 12个字符
    }
    
    @Test
    fun `分类ID应该有正确的前缀`() {
        // Arrange & Act
        val id = IdGenerator.generateCategoryId()
        
        // Assert
        assertTrue(id.startsWith("category_"))
        assertEquals(21, id.length) // "category_" + 12个字符
    }
}

package com.homework.assistant.shared.data.model

import com.homework.assistant.shared.utils.IdGenerator
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 生字数据模型测试
 */
class WordTest {
    
    @Test
    fun `创建生字对象应该包含所有必要字段`() {
        // Arrange
        val id = IdGenerator.generateWordId()
        val character = "学"
        val pinyin = "xué"
        val meaning = "学习、学问"
        val category = "一年级上册"
        
        // Act
        val word = Word(
            id = id,
            character = character,
            pinyin = pinyin,
            meaning = meaning,
            category = category,
            difficulty = 2,
            strokeCount = 8,
            radicals = "子",
            examples = listOf("学习", "学校", "学生")
        )
        
        // Assert
        assertEquals(id, word.id)
        assertEquals(character, word.character)
        assertEquals(pinyin, word.pinyin)
        assertEquals(meaning, word.meaning)
        assertEquals(category, word.category)
        assertEquals(2, word.difficulty)
        assertEquals(8, word.strokeCount)
        assertEquals("子", word.radicals)
        assertEquals(3, word.examples.size)
        assertTrue(word.examples.contains("学习"))
        assertNotNull(word.createdAt)
        assertNotNull(word.updatedAt)
    }
    
    @Test
    fun `生字对象应该有默认值`() {
        // Arrange & Act
        val word = Word(
            id = "test_id",
            character = "字",
            pinyin = "zì",
            meaning = "文字"
        )
        
        // Assert
        assertEquals("默认", word.category)
        assertEquals(1, word.difficulty)
        assertEquals(0, word.strokeCount)
        assertEquals("", word.radicals)
        assertTrue(word.examples.isEmpty())
    }
    
    @Test
    fun `生字分类对象应该包含所有必要字段`() {
        // Arrange
        val id = IdGenerator.generateCategoryId()
        val name = "一年级上册"
        val description = "小学一年级上学期生字"
        val grade = "一年级"
        val semester = "上学期"
        
        // Act
        val category = WordCategory(
            id = id,
            name = name,
            description = description,
            grade = grade,
            semester = semester,
            order = 1
        )
        
        // Assert
        assertEquals(id, category.id)
        assertEquals(name, category.name)
        assertEquals(description, category.description)
        assertEquals(grade, category.grade)
        assertEquals(semester, category.semester)
        assertEquals(1, category.order)
        assertNotNull(category.createdAt)
    }
    
    @Test
    fun `生字分类对象应该有默认值`() {
        // Arrange & Act
        val category = WordCategory(
            id = "test_id",
            name = "测试分类"
        )
        
        // Assert
        assertEquals("", category.description)
        assertEquals("", category.grade)
        assertEquals("", category.semester)
        assertEquals(0, category.order)
    }
}

package com.homework.assistant.shared.voice

import com.homework.assistant.shared.voice.impl.VoiceCommandServiceImpl
import com.homework.assistant.shared.voice.model.VoiceCommandType
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 语音指令识别服务测试
 */
class VoiceCommandServiceTest {
    
    private val voiceCommandService = VoiceCommandServiceImpl()
    
    @Test
    fun `识别下一个词指令`() = runTest {
        // Arrange
        val testTexts = listOf("下一个", "下个词", "下一题", "下一个词")
        
        // Act & Assert
        testTexts.forEach { text ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(VoiceCommandType.NEXT_WORD, command.type)
            assertEquals(text, command.originalText)
            assertTrue(command.confidence > 0.0f) // 简化置信度检查
        }
    }
    
    @Test
    fun `识别上一个词指令`() = runTest {
        // Arrange
        val testTexts = listOf("上一个", "上个词", "前一个", "上一题")
        
        // Act & Assert
        testTexts.forEach { text ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(VoiceCommandType.PREVIOUS_WORD, command.type)
            assertEquals(text, command.originalText)
            assertTrue(command.confidence > 0.0f)
        }
    }
    
    @Test
    fun `识别重复指令`() = runTest {
        // Arrange
        val testTexts = listOf("重复", "再说一遍", "再来一遍", "重新说")
        
        // Act & Assert
        testTexts.forEach { text ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(VoiceCommandType.REPEAT_WORD, command.type)
            assertTrue(command.confidence > 0.0f)
        }
    }
    
    @Test
    fun `识别背诵相关指令`() = runTest {
        // Arrange
        val testCases = mapOf(
            "开始背诵" to VoiceCommandType.START_RECITATION,
            "暂停背诵" to VoiceCommandType.PAUSE_RECITATION,
            "继续背诵" to VoiceCommandType.RESUME_RECITATION,
            "重新开始" to VoiceCommandType.RESTART_RECITATION,
            "背完了" to VoiceCommandType.FINISH_RECITATION,
            "给个提示" to VoiceCommandType.GIVE_HINT
        )
        
        // Act & Assert
        testCases.forEach { (text, expectedType) ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(expectedType, command.type)
            assertTrue(command.confidence > 0.0f)
        }
    }
    
    @Test
    fun `识别通用指令`() = runTest {
        // Arrange
        val testCases = mapOf(
            "帮助" to VoiceCommandType.HELP,
            "取消" to VoiceCommandType.CANCEL,
            "确认" to VoiceCommandType.CONFIRM
        )
        
        // Act & Assert
        testCases.forEach { (text, expectedType) ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(expectedType, command.type)
            assertTrue(command.confidence > 0.0f)
        }
    }
    
    @Test
    fun `识别未知指令`() = runTest {
        // Arrange
        val unknownTexts = listOf("随便说点什么", "这是什么意思", "天气怎么样")
        
        // Act & Assert
        unknownTexts.forEach { text ->
            val command = voiceCommandService.recognizeCommand(text)
            assertEquals(VoiceCommandType.UNKNOWN, command.type)
            assertEquals(0.0f, command.confidence)
        }
    }
    
    @Test
    fun `批量识别指令`() = runTest {
        // Arrange
        val texts = listOf("下一个", "重复", "帮助", "随便说")
        val expectedTypes = listOf(
            VoiceCommandType.NEXT_WORD,
            VoiceCommandType.REPEAT_WORD,
            VoiceCommandType.HELP,
            VoiceCommandType.UNKNOWN
        )
        
        // Act
        val commands = voiceCommandService.recognizeCommands(texts)
        
        // Assert
        assertEquals(4, commands.size)
        commands.forEachIndexed { index, command ->
            assertEquals(expectedTypes[index], command.type)
        }
    }
    
    @Test
    fun `添加自定义指令模式`() = runTest {
        // Arrange
        voiceCommandService.addCustomCommandPattern("自定义指令", "NEXT_WORD")
        
        // Act
        val command = voiceCommandService.recognizeCommand("自定义指令")
        
        // Assert
        assertEquals(VoiceCommandType.NEXT_WORD, command.type)
        assertEquals(0.9f, command.confidence)
    }
    
    @Test
    fun `设置置信度阈值`() = runTest {
        // Arrange
        voiceCommandService.setConfidenceThreshold(0.9f)
        
        // Act
        val command = voiceCommandService.recognizeCommand("下一个")
        
        // Assert
        // 由于阈值设置为0.9，而实际置信度可能低于这个值，所以可能返回UNKNOWN
        assertTrue(command.type == VoiceCommandType.NEXT_WORD || command.type == VoiceCommandType.UNKNOWN)
    }
    
    @Test
    fun `获取支持的指令列表`() = runTest {
        // Act
        val supportedCommands = voiceCommandService.getSupportedCommands()
        
        // Assert
        assertTrue(supportedCommands.isNotEmpty())
        assertTrue(supportedCommands.any { it.contains("下一个") })
        assertTrue(supportedCommands.any { it.contains("重复") })
    }
}

// 简单的测试运行器，用于支持协程测试
private fun runTest(block: suspend () -> Unit) {
    // 在实际项目中，应该使用 kotlinx-coroutines-test 的 runTest
    // 这里简化处理
    kotlinx.coroutines.runBlocking {
        block()
    }
}

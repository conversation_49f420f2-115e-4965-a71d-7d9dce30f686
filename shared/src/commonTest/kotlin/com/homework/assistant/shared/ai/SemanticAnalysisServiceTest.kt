package com.homework.assistant.shared.ai

import com.homework.assistant.shared.ai.impl.SemanticAnalysisServiceImpl
import com.homework.assistant.shared.ai.model.Intent
import com.homework.assistant.shared.ai.model.Sentiment
import com.homework.assistant.shared.ai.model.SimilarityMethod
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 语义分析服务测试
 */
class SemanticAnalysisServiceTest {
    
    private val semanticService = SemanticAnalysisServiceImpl()
    
    @Test
    fun `分析文本语义`() = runTest {
        // Arrange
        val text = "我很喜欢学习新的生字"
        
        // Act
        val result = semanticService.analyzeSemantics(text)
        
        // Assert
        assertEquals(text, result.originalText)
        assertEquals(Sentiment.POSITIVE, result.sentiment)
        assertTrue(result.confidence > 0.0f)
        assertTrue(result.keywords.isNotEmpty())
        assertTrue(result.processingTime >= 0)
    }
    
    @Test
    fun `计算文本相似度`() = runTest {
        // Arrange
        val text1 = "春眠不觉晓"
        val text2 = "春眠不觉晓，处处闻啼鸟"
        
        // Act
        val similarity = semanticService.calculateSimilarity(text1, text2)
        
        // Assert
        assertEquals(text1, similarity.text1)
        assertEquals(text2, similarity.text2)
        assertEquals(SimilarityMethod.EDIT_DISTANCE, similarity.method)
        assertTrue(similarity.similarity > 0.5f) // 应该有较高相似度
        assertTrue(similarity.details?.commonWords?.isNotEmpty() == true)
    }
    
    @Test
    fun `计算相同文本的相似度应该为1`() = runTest {
        // Arrange
        val text = "床前明月光"
        
        // Act
        val similarity = semanticService.calculateSimilarity(text, text)
        
        // Assert
        assertEquals(1.0f, similarity.similarity)
    }
    
    @Test
    fun `计算完全不同文本的相似度应该较低`() = runTest {
        // Arrange
        val text1 = "春天"
        val text2 = "数学"
        
        // Act
        val similarity = semanticService.calculateSimilarity(text1, text2)
        
        // Assert
        assertTrue(similarity.similarity < 0.5f)
    }
    
    @Test
    fun `识别用户意图`() = runTest {
        // Arrange
        val testCases = mapOf(
            "开始听写" to Intent.START_DICTATION,
            "开始背诵" to Intent.START_RECITATION,
            "给个提示" to Intent.REQUEST_HINT,
            "怎么用" to Intent.REQUEST_HELP,
            "确认" to Intent.CONFIRM_ACTION,
            "取消" to Intent.CANCEL_ACTION,
            "这是什么意思？" to Intent.ASK_QUESTION
        )
        
        // Act & Assert
        testCases.forEach { (text, expectedIntent) ->
            val result = semanticService.recognizeIntent(text)
            assertEquals(expectedIntent, result.intent)
            assertTrue(result.confidence > 0.0f)
        }
    }
    
    @Test
    fun `识别未知意图`() = runTest {
        // Arrange
        val text = "今天天气真好"
        
        // Act
        val result = semanticService.recognizeIntent(text)
        
        // Assert
        assertEquals(Intent.UNKNOWN, result.intent)
        assertTrue(result.confidence < 0.5f)
    }
    
    @Test
    fun `提取关键词`() = runTest {
        // Arrange
        val text = "小学生学习语文生字很重要"
        
        // Act
        val keywords = semanticService.extractKeywords(text, 5)
        
        // Assert
        assertTrue(keywords.isNotEmpty())
        assertTrue(keywords.size <= 5)
        assertTrue(keywords.any { it.contains("学习") || it.contains("生字") || it.contains("语文") })
    }
    
    @Test
    fun `文本纠错`() = runTest {
        // Arrange
        val text = "我在学校学习"
        
        // Act
        val correctedText = semanticService.correctText(text)
        
        // Assert
        // 简单的纠错测试，实际实现可能会更复杂
        assertTrue(correctedText.isNotEmpty())
    }
    
    @Test
    fun `判断背诵是否完整`() = runTest {
        // Arrange
        val originalText = "床前明月光，疑是地上霜"
        val completeRecitation = "床前明月光，疑是地上霜"
        val incompleteRecitation = "床前明月光"
        
        // Act
        val isComplete1 = semanticService.isRecitationComplete(originalText, completeRecitation)
        val isComplete2 = semanticService.isRecitationComplete(originalText, incompleteRecitation)
        
        // Assert
        assertTrue(isComplete1)
        assertTrue(!isComplete2)
    }
    
    @Test
    fun `判断背诵完整性带容错`() = runTest {
        // Arrange
        val originalText = "床前明月光，疑是地上霜"
        val slightlyDifferentRecitation = "床前明月光疑是地上霜" // 缺少标点
        
        // Act
        val isComplete = semanticService.isRecitationComplete(
            originalText, 
            slightlyDifferentRecitation, 
            tolerance = 0.2f
        )
        
        // Assert
        assertTrue(isComplete) // 应该在容错范围内
    }
    
    @Test
    fun `生成背诵提示`() = runTest {
        // Arrange
        val originalText = "春眠不觉晓，处处闻啼鸟"
        val currentProgress = "春眠不觉晓"
        
        // Act
        val hint = semanticService.generateRecitationHint(
            originalText, 
            currentProgress, 
            HintType.NEXT_WORD
        )
        
        // Assert
        assertTrue(hint.isNotEmpty())
        assertTrue(hint.contains("处处") || hint.contains("下一个"))
    }
    
    @Test
    fun `生成背诵提示当已完成时`() = runTest {
        // Arrange
        val originalText = "春眠不觉晓"
        val currentProgress = "春眠不觉晓"
        
        // Act
        val hint = semanticService.generateRecitationHint(
            originalText, 
            currentProgress, 
            HintType.NEXT_WORD
        )
        
        // Assert
        assertTrue(hint.contains("完成") || hint.contains("背完"))
    }
}

// 简单的测试运行器
private fun runTest(block: suspend () -> Unit) {
    kotlinx.coroutines.runBlocking {
        block()
    }
}

package com.homework.assistant.shared.data.model

import com.homework.assistant.shared.utils.IdGenerator
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 学习会话数据模型测试
 */
class StudySessionTest {
    
    @Test
    fun `创建学习会话应该包含所有必要字段`() {
        // Arrange
        val id = IdGenerator.generateSessionId()
        val contentId = IdGenerator.generateWordId()
        val startTime = System.currentTimeMillis()
        val endTime = startTime + 300000 // 5分钟后
        
        // Act
        val session = StudySession(
            id = id,
            type = StudyType.DICTATION,
            contentId = contentId,
            startTime = startTime,
            endTime = endTime,
            duration = endTime - startTime,
            status = StudyStatus.COMPLETED,
            score = 85,
            correctCount = 8,
            totalCount = 10,
            mistakes = listOf("错误1", "错误2"),
            hints = listOf("提示1"),
            notes = "学习笔记"
        )
        
        // Assert
        assertEquals(id, session.id)
        assertEquals(StudyType.DICTATION, session.type)
        assertEquals(contentId, session.contentId)
        assertEquals(startTime, session.startTime)
        assertEquals(endTime, session.endTime)
        assertEquals(300000L, session.duration)
        assertEquals(StudyStatus.COMPLETED, session.status)
        assertEquals(85, session.score)
        assertEquals(8, session.correctCount)
        assertEquals(10, session.totalCount)
        assertEquals(2, session.mistakes.size)
        assertEquals(1, session.hints.size)
        assertEquals("学习笔记", session.notes)
        assertNotNull(session.createdAt)
    }
    
    @Test
    fun `学习会话应该有默认值`() {
        // Arrange & Act
        val session = StudySession(
            id = "test_id",
            type = StudyType.RECITATION,
            contentId = "content_id",
            startTime = System.currentTimeMillis()
        )
        
        // Assert
        assertEquals(null, session.endTime)
        assertEquals(0L, session.duration)
        assertEquals(StudyStatus.IN_PROGRESS, session.status)
        assertEquals(null, session.score)
        assertEquals(0, session.correctCount)
        assertEquals(0, session.totalCount)
        assertTrue(session.mistakes.isEmpty())
        assertTrue(session.hints.isEmpty())
        assertEquals("", session.notes)
    }
    
    @Test
    fun `学习统计数据应该正确计算`() {
        // Arrange & Act
        val stats = StudyStatistics(
            totalSessions = 10,
            totalDuration = 3600000, // 1小时
            averageScore = 85.5,
            dictationStats = StudyTypeStats(
                totalSessions = 6,
                totalDuration = 2400000, // 40分钟
                averageScore = 88.0,
                bestScore = 95,
                completedCount = 5
            ),
            recitationStats = StudyTypeStats(
                totalSessions = 4,
                totalDuration = 1200000, // 20分钟
                averageScore = 82.0,
                bestScore = 90,
                completedCount = 3
            ),
            streak = 7
        )
        
        // Assert
        assertEquals(10, stats.totalSessions)
        assertEquals(3600000L, stats.totalDuration)
        assertEquals(85.5, stats.averageScore)
        assertEquals(6, stats.dictationStats.totalSessions)
        assertEquals(4, stats.recitationStats.totalSessions)
        assertEquals(7, stats.streak)
    }
    
    @Test
    fun `每日进度应该正确记录`() {
        // Arrange & Act
        val progress = DailyProgress(
            date = "2025-01-02",
            sessionCount = 3,
            duration = 1800000, // 30分钟
            averageScore = 87.5
        )
        
        // Assert
        assertEquals("2025-01-02", progress.date)
        assertEquals(3, progress.sessionCount)
        assertEquals(1800000L, progress.duration)
        assertEquals(87.5, progress.averageScore)
    }
}

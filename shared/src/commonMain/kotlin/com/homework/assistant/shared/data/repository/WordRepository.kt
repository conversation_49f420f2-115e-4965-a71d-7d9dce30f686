package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.WordCategory
import kotlinx.coroutines.flow.Flow

/**
 * 生字数据仓库接口
 */
interface WordRepository {
    
    /**
     * 获取所有生字
     */
    fun getAllWords(): Flow<List<Word>>
    
    /**
     * 根据分类获取生字
     */
    fun getWordsByCategory(categoryId: String): Flow<List<Word>>
    
    /**
     * 根据ID获取生字
     */
    suspend fun getWordById(id: String): Word?
    
    /**
     * 搜索生字
     */
    fun searchWords(query: String): Flow<List<Word>>
    
    /**
     * 添加生字
     */
    suspend fun insertWord(word: Word): Result<Unit>
    
    /**
     * 更新生字
     */
    suspend fun updateWord(word: Word): Result<Unit>
    
    /**
     * 删除生字
     */
    suspend fun deleteWord(id: String): Result<Unit>
    
    /**
     * 批量插入生字
     */
    suspend fun insertWords(words: List<Word>): Result<Unit>
    
    /**
     * 获取所有分类
     */
    fun getAllCategories(): Flow<List<WordCategory>>
    
    /**
     * 添加分类
     */
    suspend fun insertCategory(category: WordCategory): Result<Unit>
    
    /**
     * 更新分类
     */
    suspend fun updateCategory(category: WordCategory): Result<Unit>
    
    /**
     * 删除分类
     */
    suspend fun deleteCategory(id: String): Result<Unit>
    
    /**
     * 获取分类下的生字数量
     */
    suspend fun getWordCountByCategory(categoryId: String): Int
}

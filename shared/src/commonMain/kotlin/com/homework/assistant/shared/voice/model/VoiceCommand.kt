package com.homework.assistant.shared.voice.model

import kotlinx.serialization.Serializable

/**
 * 语音指令类型
 */
@Serializable
enum class VoiceCommandType {
    // 听写相关指令
    NEXT_WORD,          // 下一个词
    PREVIOUS_WORD,      // 上一个词
    REPEAT_WORD,        // 重复当前词
    SPELL_WORD,         // 拼读当前词
    SHOW_MEANING,       // 显示词义
    PAUSE_DICTATION,    // 暂停听写
    RESUME_DICTATION,   // 继续听写
    FINISH_DICTATION,   // 结束听写
    
    // 背诵相关指令
    START_RECITATION,   // 开始背诵
    PAUSE_RECITATION,   // 暂停背诵
    RESUME_RECITATION,  // 继续背诵
    RESTART_RECITATION, // 重新开始背诵
    FINISH_RECITATION,  // 结束背诵
    GIVE_HINT,          // 给提示
    
    // 通用指令
    HELP,               // 帮助
    CANCEL,             // 取消
    CONFIRM,            // 确认
    UNKNOWN             // 未知指令
}

/**
 * 语音指令识别结果
 */
@Serializable
data class VoiceCommand(
    val type: VoiceCommandType,
    val originalText: String,       // 原始语音文本
    val confidence: Float,          // 识别置信度 0.0-1.0
    val parameters: Map<String, String> = emptyMap(), // 指令参数
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 语音识别结果
 */
@Serializable
data class SpeechRecognitionResult(
    val text: String,               // 识别的文本
    val confidence: Float,          // 置信度 0.0-1.0
    val isFinal: Boolean = false,   // 是否为最终结果
    val alternatives: List<String> = emptyList(), // 备选结果
    val duration: Long = 0,         // 识别耗时（毫秒）
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 语音合成请求
 */
@Serializable
data class TextToSpeechRequest(
    val text: String,               // 要合成的文本
    val language: String = "zh-CN", // 语言
    val voice: String = "default",  // 语音类型
    val speed: Float = 1.0f,        // 语速 0.5-2.0
    val pitch: Float = 1.0f,        // 音调 0.5-2.0
    val volume: Float = 1.0f        // 音量 0.0-1.0
)

/**
 * 语音合成结果
 */
@Serializable
data class TextToSpeechResult(
    val success: Boolean,
    val duration: Long = 0,         // 播放时长（毫秒）
    val error: String? = null
)

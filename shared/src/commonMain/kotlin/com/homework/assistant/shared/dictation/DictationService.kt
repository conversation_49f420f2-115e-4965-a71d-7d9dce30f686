package com.homework.assistant.shared.dictation

import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.StudySession

import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.Serializable

/**
 * 听写会话
 */
@Serializable
data class DictationSession(
    val id: String,
    val words: List<Word>,
    val settings: DictationSettings,
    val startTime: Long = System.currentTimeMillis(),
    var endTime: Long? = null,
    var currentIndex: Int = 0,
    val results: MutableList<DictationResult> = mutableListOf(),
    var state: DictationSessionState = DictationSessionState.READY
)

/**
 * 听写会话状态
 */
@Serializable
enum class DictationSessionState {
    READY,          // 准备就绪
    SPEAKING,       // 正在播报
    LISTENING,      // 等待输入
    CHECKING,       // 检查答案
    PAUSED,         // 已暂停
    COMPLETED,      // 已完成
    CANCELLED       // 已取消
}

/**
 * 听写状态
 */
@Serializable
data class DictationState(
    val session: DictationSession?,
    val currentWord: Word?,
    val isPlaying: Boolean = false,
    val isListening: Boolean = false,
    val lastResult: DictationResult? = null,
    val progress: DictationProgress,
    val availableCommands: List<String> = emptyList()
)

/**
 * 听写结果
 */
@Serializable
data class DictationResult(
    val word: Word,
    val userAnswer: String,
    val isCorrect: Boolean,
    val score: Int,                     // 得分 0-100
    val timeSpent: Long,                // 用时（毫秒）
    val hintsUsed: Int = 0,             // 使用的提示次数
    val attempts: Int = 1,              // 尝试次数
    val feedback: String = "",          // 反馈信息
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 听写服务接口
 * 负责管理听写生字的核心业务逻辑
 */
interface DictationService {
    
    /**
     * 开始听写会话
     * @param words 要听写的生字列表
     * @param settings 听写设置
     * @return 听写会话
     */
    suspend fun startDictation(
        words: List<Word>,
        settings: DictationSettings = DictationSettings()
    ): DictationSession
    
    /**
     * 获取当前听写状态流
     */
    fun getDictationState(): Flow<DictationState>
    
    /**
     * 播报当前生字
     */
    suspend fun speakCurrentWord(): Result<Unit>
    
    /**
     * 重复播报当前生字
     */
    suspend fun repeatCurrentWord(): Result<Unit>
    
    /**
     * 播报生字拼音
     */
    suspend fun speakWordPinyin(): Result<Unit>
    
    /**
     * 显示生字含义
     */
    suspend fun showWordMeaning(): Result<String>
    
    /**
     * 下一个生字
     */
    suspend fun nextWord(): Result<Unit>
    
    /**
     * 上一个生字
     */
    suspend fun previousWord(): Result<Unit>
    
    /**
     * 提交听写答案
     * @param answer 用户输入的答案
     */
    suspend fun submitAnswer(answer: String): Result<DictationResult>
    
    /**
     * 暂停听写
     */
    suspend fun pauseDictation(): Result<Unit>
    
    /**
     * 恢复听写
     */
    suspend fun resumeDictation(): Result<Unit>
    
    /**
     * 结束听写
     */
    suspend fun finishDictation(): Result<StudySession>
    
    /**
     * 获取听写进度
     */
    suspend fun getProgress(): DictationProgress
    
    /**
     * 处理语音指令
     * @param command 语音指令文本
     */
    suspend fun handleVoiceCommand(command: String): Result<Unit>
}

/**
 * 听写设置
 */
@Serializable
data class DictationSettings(
    val speakingSpeed: Float = 1.0f,        // 语音播放速度
    val repeatCount: Int = 2,               // 默认重复次数
    val pauseDuration: Long = 3000,         // 暂停时长（毫秒）
    val autoNext: Boolean = true,           // 自动下一个
    val showPinyin: Boolean = false,        // 显示拼音提示
    val enableHints: Boolean = true,        // 启用提示
    val maxHints: Int = 3,                  // 最大提示次数
    val strictMode: Boolean = false         // 严格模式（要求完全正确）
)

/**
 * 听写进度
 */
@Serializable
data class DictationProgress(
    val currentIndex: Int,                  // 当前生字索引
    val totalCount: Int,                    // 总生字数量
    val correctCount: Int,                  // 正确数量
    val wrongCount: Int,                    // 错误数量
    val skippedCount: Int,                  // 跳过数量
    val hintsUsed: Int,                     // 使用的提示次数
    val elapsedTime: Long,                  // 已用时间（毫秒）
    val averageTimePerWord: Long            // 平均每个生字用时
) {
    val completionRate: Float
        get() = if (totalCount > 0) (currentIndex + 1).toFloat() / totalCount else 0f
    
    val accuracyRate: Float
        get() = if (currentIndex > 0) correctCount.toFloat() / currentIndex else 0f
}

package com.homework.assistant.shared.voice

import com.homework.assistant.shared.voice.model.TextToSpeechRequest
import com.homework.assistant.shared.voice.model.TextToSpeechResult
import kotlinx.coroutines.flow.Flow

/**
 * 语音合成服务接口
 */
interface TextToSpeechService {
    
    /**
     * 合成并播放语音
     * @param request 语音合成请求
     * @return 合成结果
     */
    suspend fun speak(request: TextToSpeechRequest): TextToSpeechResult
    
    /**
     * 合成并播放语音（简化版本）
     * @param text 要播放的文本
     * @param speed 语速，默认1.0
     * @return 合成结果
     */
    suspend fun speak(text: String, speed: Float = 1.0f): TextToSpeechResult
    
    /**
     * 停止当前播放
     */
    suspend fun stop()
    
    /**
     * 暂停播放
     */
    suspend fun pause()
    
    /**
     * 继续播放
     */
    suspend fun resume()
    
    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean
    
    /**
     * 获取可用的语音列表
     */
    suspend fun getAvailableVoices(): List<VoiceInfo>
    
    /**
     * 设置默认语音
     */
    suspend fun setDefaultVoice(voiceId: String)
    
    /**
     * 获取播放进度流
     */
    fun getPlaybackProgress(): Flow<PlaybackProgress>
}

/**
 * 语音信息
 */
data class VoiceInfo(
    val id: String,
    val name: String,
    val language: String,
    val gender: VoiceGender,
    val quality: VoiceQuality
)

/**
 * 语音性别
 */
enum class VoiceGender {
    MALE,
    FEMALE,
    NEUTRAL
}

/**
 * 语音质量
 */
enum class VoiceQuality {
    LOW,
    NORMAL,
    HIGH,
    VERY_HIGH
}

/**
 * 播放进度
 */
data class PlaybackProgress(
    val currentPosition: Long,  // 当前播放位置（毫秒）
    val totalDuration: Long,    // 总时长（毫秒）
    val isPlaying: Boolean,     // 是否正在播放
    val text: String           // 当前播放的文本
)

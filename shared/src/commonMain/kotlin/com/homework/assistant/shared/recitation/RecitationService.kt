package com.homework.assistant.shared.recitation

import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.StudySession
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.Serializable

/**
 * 背诵会话
 */
@Serializable
data class RecitationSession(
    val id: String,
    val recitation: Recitation,
    val settings: RecitationSettings,
    val startTime: Long = System.currentTimeMillis(),
    var endTime: Long? = null,
    var currentSentenceIndex: Int = 0,
    var currentProgress: String = "",
    val attempts: MutableList<RecitationAttempt> = mutableListOf(),
    var state: RecitationSessionState = RecitationSessionState.READY
)

/**
 * 背诵会话状态
 */
@Serializable
enum class RecitationSessionState {
    READY,          // 准备就绪
    PROMPTING,      // 正在提示
    LISTENING,      // 等待背诵
    CHECKING,       // 检查背诵
    PAUSED,         // 已暂停
    COMPLETED,      // 已完成
    CANCELLED       // 已取消
}

/**
 * 背诵状态
 */
@Serializable
data class RecitationState(
    val session: RecitationSession?,
    val currentSentence: String?,
    val isPrompting: Boolean = false,
    val isListening: Boolean = false,
    val lastAttempt: RecitationAttempt? = null,
    val progress: RecitationProgress,
    val availableCommands: List<String> = emptyList()
)

/**
 * 背诵尝试
 */
@Serializable
data class RecitationAttempt(
    val userInput: String,
    val expectedText: String,
    val similarity: Float,              // 相似度 0.0-1.0
    val isCorrect: Boolean,
    val score: Int,                     // 得分 0-100
    val timeSpent: Long,                // 用时（毫秒）
    val hintsUsed: Int = 0,             // 使用的提示次数
    val feedback: String = "",          // 反馈信息
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 背诵设置
 */
@Serializable
data class RecitationSettings(
    val promptSpeed: Float = 1.0f,      // 提示语音速度
    val similarityThreshold: Float = 0.8f, // 相似度阈值
    val enableHints: Boolean = true,     // 启用提示
    val maxHints: Int = 3,              // 最大提示次数
    val autoNext: Boolean = true,        // 自动下一句
    val strictMode: Boolean = false,     // 严格模式
    val allowSkip: Boolean = true        // 允许跳过
)

/**
 * 背诵进度
 */
@Serializable
data class RecitationProgress(
    val currentSentenceIndex: Int,      // 当前句子索引
    val totalSentences: Int,            // 总句子数量
    val completedSentences: Int,        // 完成句子数量
    val correctAttempts: Int,           // 正确尝试次数
    val totalAttempts: Int,             // 总尝试次数
    val hintsUsed: Int,                 // 使用的提示次数
    val elapsedTime: Long,              // 已用时间（毫秒）
    val averageTimePerSentence: Long    // 平均每句用时
) {
    val completionRate: Float
        get() = if (totalSentences > 0) completedSentences.toFloat() / totalSentences else 0f
    
    val accuracyRate: Float
        get() = if (totalAttempts > 0) correctAttempts.toFloat() / totalAttempts else 0f
}

/**
 * 背诵服务接口
 * 负责管理背诵古诗的核心业务逻辑
 */
interface RecitationService {
    
    /**
     * 开始背诵会话
     * @param recitation 要背诵的内容
     * @param settings 背诵设置
     * @return 背诵会话
     */
    suspend fun startRecitation(
        recitation: Recitation,
        settings: RecitationSettings = RecitationSettings()
    ): RecitationSession
    
    /**
     * 获取当前背诵状态流
     */
    fun getRecitationState(): Flow<RecitationState>
    
    /**
     * 播报当前句子提示
     */
    suspend fun speakCurrentSentence(): Result<Unit>
    
    /**
     * 重复播报当前句子
     */
    suspend fun repeatCurrentSentence(): Result<Unit>
    
    /**
     * 播报整首诗
     */
    suspend fun speakFullPoem(): Result<Unit>
    
    /**
     * 给出提示（下一个字或词）
     */
    suspend fun giveHint(): Result<String>
    
    /**
     * 下一句
     */
    suspend fun nextSentence(): Result<Unit>
    
    /**
     * 上一句
     */
    suspend fun previousSentence(): Result<Unit>
    
    /**
     * 提交背诵内容
     * @param userInput 用户背诵的内容
     */
    suspend fun submitRecitation(userInput: String): Result<RecitationAttempt>
    
    /**
     * 暂停背诵
     */
    suspend fun pauseRecitation(): Result<Unit>
    
    /**
     * 恢复背诵
     */
    suspend fun resumeRecitation(): Result<Unit>
    
    /**
     * 结束背诵
     */
    suspend fun finishRecitation(): Result<StudySession>
    
    /**
     * 获取背诵进度
     */
    suspend fun getProgress(): RecitationProgress
    
    /**
     * 处理语音指令
     * @param command 语音指令文本
     */
    suspend fun handleVoiceCommand(command: String): Result<Unit>
    
    /**
     * 跳过当前句子
     */
    suspend fun skipCurrentSentence(): Result<Unit>
    
    /**
     * 重新开始背诵
     */
    suspend fun restartRecitation(): Result<Unit>
}

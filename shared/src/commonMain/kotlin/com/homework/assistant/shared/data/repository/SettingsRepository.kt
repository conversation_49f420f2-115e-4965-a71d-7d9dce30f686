package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 设置数据仓库接口
 */
interface SettingsRepository {
    
    /**
     * 获取应用设置
     */
    fun getAppSettings(): Flow<AppSettings>
    
    /**
     * 更新应用设置
     */
    suspend fun updateAppSettings(settings: AppSettings): Result<Unit>
    
    /**
     * 更新通用设置
     */
    suspend fun updateGeneralSettings(settings: GeneralSettings): Result<Unit>
    
    /**
     * 更新听写设置
     */
    suspend fun updateDictationSettings(settings: DictationSettings): Result<Unit>
    
    /**
     * 更新背诵设置
     */
    suspend fun updateRecitationSettings(settings: RecitationSettings): Result<Unit>
    
    /**
     * 更新语音设置
     */
    suspend fun updateVoiceSettings(settings: VoiceSettings): Result<Unit>
    
    /**
     * 更新UI设置
     */
    suspend fun updateUISettings(settings: UISettings): Result<Unit>
    
    /**
     * 更新隐私设置
     */
    suspend fun updatePrivacySettings(settings: PrivacySettings): Result<Unit>
    
    /**
     * 重置为默认设置
     */
    suspend fun resetToDefaults(): Result<Unit>
    
    /**
     * 导出设置
     */
    suspend fun exportSettings(): Result<String>
    
    /**
     * 导入设置
     */
    suspend fun importSettings(settingsJson: String): Result<Unit>
}

package com.homework.assistant.shared.voice.impl

import com.homework.assistant.shared.voice.VoiceCommandService
import com.homework.assistant.shared.voice.model.VoiceCommand
import com.homework.assistant.shared.voice.model.VoiceCommandType

/**
 * 语音指令识别服务实现
 */
class VoiceCommandServiceImpl : VoiceCommandService {
    
    private var confidenceThreshold = 0.5f
    private val customPatterns = mutableMapOf<String, String>()
    
    // 预定义的指令模式
    private val commandPatterns = mapOf(
        // 听写相关指令
        "下一个|下个|下一个词|下个词|下一题" to VoiceCommandType.NEXT_WORD,
        "上一个|上个|上一个词|上个词|上一题|前一个" to VoiceCommandType.PREVIOUS_WORD,
        "重复|再说一遍|再来一遍|重新说" to VoiceCommandType.REPEAT_WORD,
        "拼读|怎么拼|拼音|读音" to VoiceCommandType.SPELL_WORD,
        "什么意思|含义|解释|意思" to VoiceCommandType.SHOW_MEANING,
        "暂停|停一下|等一下" to VoiceCommandType.PAUSE_DICTATION,
        "继续|开始|恢复" to VoiceCommandType.RESUME_DICTATION,
        "结束|完成|结束听写|听写完了" to VoiceCommandType.FINISH_DICTATION,
        
        // 背诵相关指令
        "开始背诵|开始背|我要背诵" to VoiceCommandType.START_RECITATION,
        "暂停背诵|背诵暂停" to VoiceCommandType.PAUSE_RECITATION,
        "继续背诵|背诵继续" to VoiceCommandType.RESUME_RECITATION,
        "重新开始|重新背|从头开始" to VoiceCommandType.RESTART_RECITATION,
        "背完了|结束背诵|背诵结束" to VoiceCommandType.FINISH_RECITATION,
        "提示|给个提示|提示一下|不会了" to VoiceCommandType.GIVE_HINT,
        
        // 通用指令
        "帮助|怎么用|使用方法" to VoiceCommandType.HELP,
        "取消|算了|不要了" to VoiceCommandType.CANCEL,
        "确认|好的|是的|对的" to VoiceCommandType.CONFIRM
    )
    
    override suspend fun recognizeCommand(text: String): VoiceCommand {
        val cleanText = text.trim().replace("，", "").replace("。", "")
        
        // 首先检查自定义模式
        for ((pattern, commandType) in customPatterns) {
            if (matchesPattern(cleanText, pattern)) {
                return VoiceCommand(
                    type = VoiceCommandType.valueOf(commandType),
                    originalText = text,
                    confidence = 0.9f
                )
            }
        }
        
        // 检查预定义模式
        for ((pattern, commandType) in commandPatterns) {
            if (matchesPattern(cleanText, pattern)) {
                val confidence = calculateConfidence(cleanText, pattern)
                if (confidence >= confidenceThreshold) {
                    return VoiceCommand(
                        type = commandType,
                        originalText = text,
                        confidence = confidence
                    )
                }
            }
        }
        
        // 如果没有匹配的指令，返回未知类型
        return VoiceCommand(
            type = VoiceCommandType.UNKNOWN,
            originalText = text,
            confidence = 0.0f
        )
    }
    
    override suspend fun recognizeCommands(texts: List<String>): List<VoiceCommand> {
        return texts.map { recognizeCommand(it) }
    }
    
    override fun addCustomCommandPattern(pattern: String, commandType: String) {
        customPatterns[pattern] = commandType
    }
    
    override fun getSupportedCommands(): List<String> {
        return commandPatterns.keys.toList() + customPatterns.keys.toList()
    }
    
    override fun setConfidenceThreshold(threshold: Float) {
        confidenceThreshold = threshold.coerceIn(0.0f, 1.0f)
    }
    
    /**
     * 检查文本是否匹配模式
     */
    private fun matchesPattern(text: String, pattern: String): Boolean {
        val keywords = pattern.split("|")
        return keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true)
        }
    }
    
    /**
     * 计算匹配置信度
     */
    private fun calculateConfidence(text: String, pattern: String): Float {
        val keywords = pattern.split("|")
        val matchedKeywords = keywords.filter { keyword ->
            text.contains(keyword, ignoreCase = true)
        }
        
        if (matchedKeywords.isEmpty()) return 0.0f
        
        // 基础置信度：匹配关键词数量 / 总关键词数量
        val baseConfidence = matchedKeywords.size.toFloat() / keywords.size
        
        // 长度匹配度：匹配的关键词长度 / 文本总长度
        val matchedLength = matchedKeywords.maxOfOrNull { it.length } ?: 0
        val lengthRatio = matchedLength.toFloat() / text.length
        
        // 综合置信度
        return (baseConfidence * 0.7f + lengthRatio * 0.3f).coerceIn(0.0f, 1.0f)
    }
}

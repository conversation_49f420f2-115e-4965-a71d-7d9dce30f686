package com.homework.assistant.shared.ai.impl

import com.homework.assistant.shared.ai.SemanticAnalysisService
import com.homework.assistant.shared.ai.HintType
import com.homework.assistant.shared.ai.model.*
import kotlin.math.max
import kotlin.math.min

/**
 * 语义分析服务实现
 * 这是一个基础实现，后续可以集成更高级的 AI 模型
 */
class SemanticAnalysisServiceImpl : SemanticAnalysisService {
    
    override suspend fun analyzeSemantics(
        text: String,
        context: String?
    ): SemanticAnalysisResult {
        val startTime = System.currentTimeMillis()
        
        val normalizedText = normalizeText(text)
        val sentiment = analyzeSentiment(normalizedText)
        val entities = extractEntities(normalizedText)
        val keywords = extractKeywords(normalizedText, 5)
        
        val processingTime = System.currentTimeMillis() - startTime
        
        return SemanticAnalysisResult(
            originalText = text,
            normalizedText = normalizedText,
            sentiment = sentiment,
            entities = entities,
            keywords = keywords,
            confidence = 0.8f,
            processingTime = processingTime
        )
    }
    
    override suspend fun calculateSimilarity(
        text1: String,
        text2: String
    ): TextSimilarity {
        val normalizedText1 = normalizeText(text1)
        val normalizedText2 = normalizeText(text2)
        
        // 使用编辑距离计算相似度
        val editDistance = calculateEditDistance(normalizedText1, normalizedText2)
        val maxLength = max(normalizedText1.length, normalizedText2.length)
        val similarity = if (maxLength == 0) 1.0f else 1.0f - (editDistance.toFloat() / maxLength)
        
        val words1 = normalizedText1.split("\\s+".toRegex()).filter { it.isNotBlank() }
        val words2 = normalizedText2.split("\\s+".toRegex()).filter { it.isNotBlank() }
        val commonWords = words1.intersect(words2.toSet()).toList()
        val uniqueWords1 = words1.subtract(words2.toSet()).toList()
        val uniqueWords2 = words2.subtract(words1.toSet()).toList()
        
        return TextSimilarity(
            text1 = text1,
            text2 = text2,
            similarity = similarity,
            method = SimilarityMethod.EDIT_DISTANCE,
            details = SimilarityDetails(
                commonWords = commonWords,
                uniqueWords1 = uniqueWords1,
                uniqueWords2 = uniqueWords2,
                editOperations = editDistance
            )
        )
    }
    
    override suspend fun recognizeIntent(
        text: String,
        domain: String
    ): IntentRecognitionResult {
        val normalizedText = normalizeText(text)
        
        // 简单的意图识别规则
        val intent = when {
            normalizedText.contains("开始") && normalizedText.contains("听写") -> Intent.START_DICTATION
            normalizedText.contains("开始") && normalizedText.contains("背诵") -> Intent.START_RECITATION
            normalizedText.contains("提示") || normalizedText.contains("不会") -> Intent.REQUEST_HINT
            normalizedText.contains("帮助") || normalizedText.contains("怎么") -> Intent.REQUEST_HELP
            normalizedText.contains("确认") || normalizedText.contains("好的") -> Intent.CONFIRM_ACTION
            normalizedText.contains("取消") || normalizedText.contains("算了") -> Intent.CANCEL_ACTION
            normalizedText.contains("？") || normalizedText.contains("什么") -> Intent.ASK_QUESTION
            else -> Intent.UNKNOWN
        }
        
        val confidence = if (intent == Intent.UNKNOWN) 0.3f else 0.8f
        
        return IntentRecognitionResult(
            intent = intent,
            confidence = confidence
        )
    }
    
    override suspend fun extractKeywords(text: String, maxKeywords: Int): List<String> {
        val normalizedText = normalizeText(text)
        val words = normalizedText.split("\\s+".toRegex())
            .filter { it.isNotBlank() && it.length > 1 }
            .groupBy { it }
            .mapValues { it.value.size }
            .toList()
            .sortedByDescending { it.second }
            .take(maxKeywords)
            .map { it.first }
        
        return words
    }
    
    override suspend fun correctText(text: String, context: String?): String {
        // 简单的文本纠错，主要处理常见的拼音输入错误
        var correctedText = text
        
        // 常见错误映射
        val corrections = mapOf(
            "的" to "地",
            "在" to "再",
            "做" to "作"
        )
        
        for ((wrong, correct) in corrections) {
            correctedText = correctedText.replace(wrong, correct)
        }
        
        return correctedText
    }
    
    override suspend fun isRecitationComplete(
        originalText: String,
        recitedText: String,
        tolerance: Float
    ): Boolean {
        val similarity = calculateSimilarity(originalText, recitedText)
        return similarity.similarity >= (1.0f - tolerance)
    }
    
    override suspend fun generateRecitationHint(
        originalText: String,
        currentProgress: String,
        hintType: HintType
    ): String {
        val originalWords = originalText.split("\\s+".toRegex()).filter { it.isNotBlank() }
        val progressWords = currentProgress.split("\\s+".toRegex()).filter { it.isNotBlank() }
        
        return when (hintType) {
            HintType.NEXT_WORD -> {
                if (progressWords.size < originalWords.size) {
                    "下一个词是：${originalWords[progressWords.size]}"
                } else {
                    "已经背完了！"
                }
            }
            HintType.NEXT_SENTENCE -> {
                val sentences = originalText.split("[。！？]".toRegex()).filter { it.isNotBlank() }
                "下一句是：${sentences.firstOrNull() ?: "没有更多内容了"}"
            }
            HintType.MEANING -> "这段话的意思是..."
            HintType.CONTEXT -> "联系上下文想想..."
            HintType.RHYME -> "注意押韵..."
        }
    }
    
    /**
     * 文本标准化
     */
    private fun normalizeText(text: String): String {
        return text.trim()
            .replace("\\s+".toRegex(), " ")
            .replace("[，。！？；：（）【】]".toRegex(), "")
    }
    
    /**
     * 情感分析
     */
    private fun analyzeSentiment(text: String): Sentiment {
        val positiveWords = listOf("好", "棒", "对", "是", "喜欢", "开心")
        val negativeWords = listOf("不", "错", "难", "不会", "不懂", "烦")
        
        val positiveCount = positiveWords.count { text.contains(it) }
        val negativeCount = negativeWords.count { text.contains(it) }
        
        return when {
            positiveCount > negativeCount -> Sentiment.POSITIVE
            negativeCount > positiveCount -> Sentiment.NEGATIVE
            else -> Sentiment.NEUTRAL
        }
    }
    
    /**
     * 实体提取
     */
    private fun extractEntities(text: String): List<Entity> {
        val entities = mutableListOf<Entity>()
        
        // 简单的数字识别
        val numberRegex = "\\d+".toRegex()
        numberRegex.findAll(text).forEach { match ->
            entities.add(
                Entity(
                    text = match.value,
                    type = EntityType.NUMBER,
                    startIndex = match.range.first,
                    endIndex = match.range.last,
                    confidence = 0.9f
                )
            )
        }
        
        return entities
    }
    
    /**
     * 计算编辑距离
     */
    private fun calculateEditDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }
        
        for (i in 0..s1.length) {
            dp[i][0] = i
        }
        for (j in 0..s2.length) {
            dp[0][j] = j
        }
        
        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                if (s1[i - 1] == s2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1]
                } else {
                    dp[i][j] = 1 + min(dp[i - 1][j], min(dp[i][j - 1], dp[i - 1][j - 1]))
                }
            }
        }
        
        return dp[s1.length][s2.length]
    }
}

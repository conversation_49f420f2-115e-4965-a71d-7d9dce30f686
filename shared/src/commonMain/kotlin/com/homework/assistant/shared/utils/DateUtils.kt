package com.homework.assistant.shared.utils

import kotlinx.datetime.*

/**
 * 日期工具类
 */
object DateUtils {
    
    /**
     * 获取当前时间戳（毫秒）
     */
    fun currentTimeMillis(): Long = Clock.System.now().toEpochMilliseconds()
    
    /**
     * 将时间戳转换为日期字符串 (YYYY-MM-DD)
     */
    fun formatDate(timestamp: Long): String {
        val instant = Instant.fromEpochMilliseconds(timestamp)
        val localDate = instant.toLocalDateTime(TimeZone.currentSystemDefault()).date
        return localDate.toString()
    }
    
    /**
     * 将时间戳转换为时间字符串 (HH:mm:ss)
     */
    fun formatTime(timestamp: Long): String {
        val instant = Instant.fromEpochMilliseconds(timestamp)
        val localTime = instant.toLocalDateTime(TimeZone.currentSystemDefault()).time
        return "${localTime.hour.toString().padStart(2, '0')}:" +
                "${localTime.minute.toString().padStart(2, '0')}:" +
                "${localTime.second.toString().padStart(2, '0')}"
    }
    
    /**
     * 将时间戳转换为日期时间字符串 (YYYY-MM-DD HH:mm:ss)
     */
    fun formatDateTime(timestamp: Long): String {
        return "${formatDate(timestamp)} ${formatTime(timestamp)}"
    }
    
    /**
     * 获取今天的开始时间戳（00:00:00）
     */
    fun getTodayStartTimestamp(): Long {
        val now = Clock.System.now()
        val today = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        val startOfDay = today.atTime(0, 0, 0)
        return startOfDay.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
    }
    
    /**
     * 获取今天的结束时间戳（23:59:59）
     */
    fun getTodayEndTimestamp(): Long {
        val now = Clock.System.now()
        val today = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        val endOfDay = today.atTime(23, 59, 59, 999_000_000)
        return endOfDay.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
    }
    
    /**
     * 获取指定天数前的时间戳
     */
    fun getDaysAgoTimestamp(days: Int): Long {
        val now = Clock.System.now()
        val period = DateTimePeriod(days = days)
        val daysAgo = now.minus(period, TimeZone.currentSystemDefault())
        return daysAgo.toEpochMilliseconds()
    }
    
    /**
     * 格式化持续时间（毫秒转换为可读格式）
     */
    fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> "${hours}小时${minutes % 60}分钟"
            minutes > 0 -> "${minutes}分钟${seconds % 60}秒"
            else -> "${seconds}秒"
        }
    }
    
    /**
     * 检查两个时间戳是否在同一天
     */
    fun isSameDay(timestamp1: Long, timestamp2: Long): Boolean {
        return formatDate(timestamp1) == formatDate(timestamp2)
    }
    
    /**
     * 获取两个时间戳之间的天数差
     */
    fun getDaysBetween(startTimestamp: Long, endTimestamp: Long): Int {
        val start = Instant.fromEpochMilliseconds(startTimestamp)
            .toLocalDateTime(TimeZone.currentSystemDefault()).date
        val end = Instant.fromEpochMilliseconds(endTimestamp)
            .toLocalDateTime(TimeZone.currentSystemDefault()).date
        return start.daysUntil(end)
    }
}

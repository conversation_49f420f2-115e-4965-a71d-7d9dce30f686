package com.homework.assistant.shared.ai.model

import kotlinx.serialization.Serializable

/**
 * 语义分析结果
 */
@Serializable
data class SemanticAnalysisResult(
    val originalText: String,
    val normalizedText: String,      // 标准化后的文本
    val sentiment: Sentiment,        // 情感倾向
    val entities: List<Entity>,      // 实体识别结果
    val keywords: List<String>,      // 关键词
    val confidence: Float,           // 分析置信度
    val processingTime: Long = 0     // 处理时间（毫秒）
)

/**
 * 情感倾向
 */
@Serializable
enum class Sentiment {
    POSITIVE,    // 积极
    NEGATIVE,    // 消极
    NEUTRAL      // 中性
}

/**
 * 实体
 */
@Serializable
data class Entity(
    val text: String,               // 实体文本
    val type: EntityType,           // 实体类型
    val startIndex: Int,            // 开始位置
    val endIndex: Int,              // 结束位置
    val confidence: Float           // 置信度
)

/**
 * 实体类型
 */
@Serializable
enum class EntityType {
    PERSON,      // 人名
    PLACE,       // 地名
    TIME,        // 时间
    NUMBER,      // 数字
    WORD,        // 生字
    POEM,        // 诗词
    OTHER        // 其他
}

/**
 * 文本相似度结果
 */
@Serializable
data class TextSimilarity(
    val text1: String,
    val text2: String,
    val similarity: Float,           // 相似度 0.0-1.0
    val method: SimilarityMethod,    // 计算方法
    val details: SimilarityDetails? = null
)

/**
 * 相似度计算方法
 */
@Serializable
enum class SimilarityMethod {
    COSINE,         // 余弦相似度
    JACCARD,        // 杰卡德相似度
    EDIT_DISTANCE,  // 编辑距离
    SEMANTIC        // 语义相似度
}

/**
 * 相似度详细信息
 */
@Serializable
data class SimilarityDetails(
    val commonWords: List<String>,   // 共同词汇
    val uniqueWords1: List<String>,  // 文本1独有词汇
    val uniqueWords2: List<String>,  // 文本2独有词汇
    val editOperations: Int = 0      // 编辑操作次数
)

/**
 * 意图识别结果
 */
@Serializable
data class IntentRecognitionResult(
    val intent: Intent,
    val confidence: Float,
    val parameters: Map<String, String> = emptyMap(),
    val alternatives: List<Intent> = emptyList()
)

/**
 * 用户意图
 */
@Serializable
enum class Intent {
    // 听写相关意图
    START_DICTATION,     // 开始听写
    ANSWER_DICTATION,    // 回答听写
    REQUEST_HINT,        // 请求提示
    
    // 背诵相关意图
    START_RECITATION,    // 开始背诵
    RECITE_CONTENT,      // 背诵内容
    REQUEST_PROMPT,      // 请求提示
    
    // 通用意图
    ASK_QUESTION,        // 提问
    EXPRESS_CONFUSION,   // 表达困惑
    REQUEST_HELP,        // 请求帮助
    CONFIRM_ACTION,      // 确认操作
    CANCEL_ACTION,       // 取消操作
    
    UNKNOWN              // 未知意图
}

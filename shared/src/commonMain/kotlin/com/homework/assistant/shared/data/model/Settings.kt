package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 应用设置数据模型
 */
@Serializable
data class AppSettings(
    val general: GeneralSettings = GeneralSettings(),
    val dictation: DictationSettings = DictationSettings(),
    val recitation: RecitationSettings = RecitationSettings(),
    val voice: VoiceSettings = VoiceSettings(),
    val ui: UISettings = UISettings(),
    val privacy: PrivacySettings = PrivacySettings(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 通用设置
 */
@Serializable
data class GeneralSettings(
    val language: String = "zh-CN",     // 语言
    val autoSave: Boolean = true,       // 自动保存
    val soundEnabled: Boolean = true,   // 声音开关
    val vibrationEnabled: Boolean = true, // 震动开关
    val darkMode: Boolean = false       // 深色模式
)

/**
 * 听写设置
 */
@Serializable
data class DictationSettings(
    val speakingSpeed: Float = 1.0f,    // 语音播放速度
    val repeatCount: Int = 2,           // 默认重复次数
    val pauseDuration: Long = 3000,     // 暂停时长（毫秒）
    val autoNext: Boolean = true,       // 自动下一个
    val showPinyin: Boolean = false,    // 显示拼音提示
    val enableHints: Boolean = true,    // 启用提示
    val maxHints: Int = 3              // 最大提示次数
)

/**
 * 背诵设置
 */
@Serializable
data class RecitationSettings(
    val timeoutDuration: Long = 30000,  // 超时时长（毫秒）
    val enablePrompts: Boolean = true,  // 启用提示
    val promptDelay: Long = 5000,      // 提示延迟（毫秒）
    val strictMode: Boolean = false,    // 严格模式
    val allowPause: Boolean = true,     // 允许暂停
    val showProgress: Boolean = true    // 显示进度
)

/**
 * 语音设置
 */
@Serializable
data class VoiceSettings(
    val voiceType: String = "default",  // 语音类型
    val pitch: Float = 1.0f,           // 音调
    val volume: Float = 1.0f,          // 音量
    val recognitionLanguage: String = "zh-CN", // 识别语言
    val recognitionTimeout: Long = 10000, // 识别超时
    val noiseReduction: Boolean = true  // 降噪
)

/**
 * UI设置
 */
@Serializable
data class UISettings(
    val fontSize: Float = 16.0f,       // 字体大小
    val theme: String = "default",     // 主题
    val showAnimations: Boolean = true, // 显示动画
    val compactMode: Boolean = false,   // 紧凑模式
    val showTutorial: Boolean = true    // 显示教程
)

/**
 * 隐私设置
 */
@Serializable
data class PrivacySettings(
    val dataCollection: Boolean = false, // 数据收集
    val crashReporting: Boolean = true,   // 崩溃报告
    val analytics: Boolean = false,       // 分析统计
    val cloudSync: Boolean = false        // 云同步
)

package com.homework.assistant.shared.ai

import com.homework.assistant.shared.ai.model.SemanticAnalysisResult
import com.homework.assistant.shared.ai.model.TextSimilarity
import com.homework.assistant.shared.ai.model.IntentRecognitionResult

/**
 * AI 语义分析服务接口
 */
interface SemanticAnalysisService {
    
    /**
     * 分析文本语义
     * @param text 要分析的文本
     * @param context 上下文信息
     * @return 语义分析结果
     */
    suspend fun analyzeSemantics(
        text: String,
        context: String? = null
    ): SemanticAnalysisResult
    
    /**
     * 计算两个文本的相似度
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度结果
     */
    suspend fun calculateSimilarity(
        text1: String,
        text2: String
    ): TextSimilarity
    
    /**
     * 识别用户意图
     * @param text 用户输入的文本
     * @param domain 领域（听写、背诵等）
     * @return 意图识别结果
     */
    suspend fun recognizeIntent(
        text: String,
        domain: String = "general"
    ): IntentRecognitionResult
    
    /**
     * 提取关键词
     * @param text 文本
     * @param maxKeywords 最大关键词数量
     * @return 关键词列表
     */
    suspend fun extractKeywords(
        text: String,
        maxKeywords: Int = 10
    ): List<String>
    
    /**
     * 文本纠错
     * @param text 原始文本
     * @param context 上下文
     * @return 纠错后的文本
     */
    suspend fun correctText(
        text: String,
        context: String? = null
    ): String
    
    /**
     * 判断背诵是否完整
     * @param originalText 原文
     * @param recitedText 背诵文本
     * @param tolerance 容错率 0.0-1.0
     * @return 是否完整背诵
     */
    suspend fun isRecitationComplete(
        originalText: String,
        recitedText: String,
        tolerance: Float = 0.1f
    ): Boolean
    
    /**
     * 生成背诵提示
     * @param originalText 原文
     * @param currentProgress 当前进度
     * @param hintType 提示类型
     * @return 提示内容
     */
    suspend fun generateRecitationHint(
        originalText: String,
        currentProgress: String,
        hintType: HintType = HintType.NEXT_WORD
    ): String
}

/**
 * 提示类型
 */
enum class HintType {
    NEXT_WORD,      // 下一个词
    NEXT_SENTENCE,  // 下一句
    RHYME,          // 押韵提示
    MEANING,        // 含义提示
    CONTEXT         // 上下文提示
}

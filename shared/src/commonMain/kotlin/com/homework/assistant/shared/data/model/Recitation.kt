package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 背诵内容数据模型
 */
@Serializable
data class Recitation(
    val id: String,
    val title: String,           // 标题
    val content: String,         // 背诵内容
    val author: String = "",     // 作者
    val source: String = "",     // 出处
    val category: String = "默认", // 分类
    val difficulty: Int = 1,     // 难度等级 1-5
    val estimatedTime: Int = 0,  // 预计背诵时间（分钟）
    val tags: List<String> = emptyList(), // 标签
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 背诵分类
 */
@Serializable
data class RecitationCategory(
    val id: String,
    val name: String,            // 分类名称
    val description: String = "", // 分类描述
    val type: RecitationType = RecitationType.POEM, // 类型
    val grade: String = "",      // 年级
    val order: Int = 0,          // 排序
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 背诵内容类型
 */
@Serializable
enum class RecitationType {
    POEM,        // 诗歌
    PROSE,       // 散文
    CLASSICAL,   // 文言文
    MODERN,      // 现代文
    SONG,        // 歌曲
    OTHER        // 其他
}

package com.homework.assistant.shared.utils

import kotlin.random.Random

/**
 * ID生成器
 * 用于生成唯一标识符
 */
object IdGenerator {
    
    private const val CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    
    /**
     * 生成随机ID
     * @param length ID长度，默认16位
     * @return 生成的ID字符串
     */
    fun generateId(length: Int = 16): String {
        return (1..length)
            .map { CHARS[Random.nextInt(CHARS.length)] }
            .joinToString("")
    }
    
    /**
     * 生成带前缀的ID
     * @param prefix 前缀
     * @param length ID部分长度，默认12位
     * @return 带前缀的ID字符串
     */
    fun generateIdWithPrefix(prefix: String, length: Int = 12): String {
        return "${prefix}_${generateId(length)}"
    }
    
    /**
     * 生成生字ID
     */
    fun generateWordId(): String = generateIdWithPrefix("word")
    
    /**
     * 生成背诵内容ID
     */
    fun generateRecitationId(): String = generateIdWithPrefix("recitation")
    
    /**
     * 生成学习会话ID
     */
    fun generateSessionId(): String = generateIdWithPrefix("session")
    
    /**
     * 生成分类ID
     */
    fun generateCategoryId(): String = generateIdWithPrefix("category")
}

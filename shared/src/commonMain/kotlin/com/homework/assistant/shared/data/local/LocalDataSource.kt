package com.homework.assistant.shared.data.local

import com.homework.assistant.shared.data.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 本地数据源接口
 * 定义本地数据库操作的抽象接口
 */
interface LocalDataSource {
    
    // ========== 生字相关 ==========
    
    /**
     * 获取所有生字
     */
    fun getAllWords(): Flow<List<Word>>
    
    /**
     * 根据分类获取生字
     */
    fun getWordsByCategory(categoryId: String): Flow<List<Word>>
    
    /**
     * 根据ID获取生字
     */
    suspend fun getWordById(id: String): Word?
    
    /**
     * 搜索生字
     */
    fun searchWords(query: String): Flow<List<Word>>
    
    /**
     * 插入生字
     */
    suspend fun insertWord(word: Word)
    
    /**
     * 更新生字
     */
    suspend fun updateWord(word: Word)
    
    /**
     * 删除生字
     */
    suspend fun deleteWord(id: String)
    
    /**
     * 批量插入生字
     */
    suspend fun insertWords(words: List<Word>)
    
    // ========== 生字分类相关 ==========
    
    /**
     * 获取所有生字分类
     */
    fun getAllWordCategories(): Flow<List<WordCategory>>
    
    /**
     * 插入生字分类
     */
    suspend fun insertWordCategory(category: WordCategory)
    
    /**
     * 更新生字分类
     */
    suspend fun updateWordCategory(category: WordCategory)
    
    /**
     * 删除生字分类
     */
    suspend fun deleteWordCategory(id: String)
    
    /**
     * 获取分类下的生字数量
     */
    suspend fun getWordCountByCategory(categoryId: String): Int
    
    // ========== 背诵内容相关 ==========
    
    /**
     * 获取所有背诵内容
     */
    fun getAllRecitations(): Flow<List<Recitation>>
    
    /**
     * 根据分类获取背诵内容
     */
    fun getRecitationsByCategory(categoryId: String): Flow<List<Recitation>>
    
    /**
     * 根据类型获取背诵内容
     */
    fun getRecitationsByType(type: RecitationType): Flow<List<Recitation>>
    
    /**
     * 根据ID获取背诵内容
     */
    suspend fun getRecitationById(id: String): Recitation?
    
    /**
     * 搜索背诵内容
     */
    fun searchRecitations(query: String): Flow<List<Recitation>>
    
    /**
     * 插入背诵内容
     */
    suspend fun insertRecitation(recitation: Recitation)
    
    /**
     * 更新背诵内容
     */
    suspend fun updateRecitation(recitation: Recitation)
    
    /**
     * 删除背诵内容
     */
    suspend fun deleteRecitation(id: String)
    
    /**
     * 批量插入背诵内容
     */
    suspend fun insertRecitations(recitations: List<Recitation>)
    
    // ========== 背诵分类相关 ==========
    
    /**
     * 获取所有背诵分类
     */
    fun getAllRecitationCategories(): Flow<List<RecitationCategory>>
    
    /**
     * 插入背诵分类
     */
    suspend fun insertRecitationCategory(category: RecitationCategory)
    
    /**
     * 更新背诵分类
     */
    suspend fun updateRecitationCategory(category: RecitationCategory)
    
    /**
     * 删除背诵分类
     */
    suspend fun deleteRecitationCategory(id: String)
    
    /**
     * 获取分类下的背诵内容数量
     */
    suspend fun getRecitationCountByCategory(categoryId: String): Int
    
    // ========== 学习会话相关 ==========
    
    /**
     * 获取所有学习会话
     */
    fun getAllStudySessions(): Flow<List<StudySession>>
    
    /**
     * 根据类型获取学习会话
     */
    fun getStudySessionsByType(type: StudyType): Flow<List<StudySession>>
    
    /**
     * 根据内容ID获取学习会话
     */
    fun getStudySessionsByContentId(contentId: String): Flow<List<StudySession>>
    
    /**
     * 根据日期范围获取学习会话
     */
    fun getStudySessionsByDateRange(startDate: Long, endDate: Long): Flow<List<StudySession>>
    
    /**
     * 根据ID获取学习会话
     */
    suspend fun getStudySessionById(id: String): StudySession?
    
    /**
     * 插入学习会话
     */
    suspend fun insertStudySession(session: StudySession)
    
    /**
     * 更新学习会话
     */
    suspend fun updateStudySession(session: StudySession)
    
    /**
     * 删除学习会话
     */
    suspend fun deleteStudySession(id: String)
    
    /**
     * 清理过期学习会话
     */
    suspend fun cleanupOldStudySessions(beforeDate: Long): Int
}

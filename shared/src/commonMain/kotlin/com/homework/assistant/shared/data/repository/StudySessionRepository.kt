package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 学习会话数据仓库接口
 */
interface StudySessionRepository {
    
    /**
     * 获取所有学习会话
     */
    fun getAllSessions(): Flow<List<StudySession>>
    
    /**
     * 根据类型获取学习会话
     */
    fun getSessionsByType(type: StudyType): Flow<List<StudySession>>
    
    /**
     * 根据内容ID获取学习会话
     */
    fun getSessionsByContentId(contentId: String): Flow<List<StudySession>>
    
    /**
     * 根据日期范围获取学习会话
     */
    fun getSessionsByDateRange(startDate: Long, endDate: Long): Flow<List<StudySession>>
    
    /**
     * 根据ID获取学习会话
     */
    suspend fun getSessionById(id: String): StudySession?
    
    /**
     * 添加学习会话
     */
    suspend fun insertSession(session: StudySession): Result<Unit>
    
    /**
     * 更新学习会话
     */
    suspend fun updateSession(session: StudySession): Result<Unit>
    
    /**
     * 删除学习会话
     */
    suspend fun deleteSession(id: String): Result<Unit>
    
    /**
     * 获取学习统计数据
     */
    suspend fun getStudyStatistics(): StudyStatistics
    
    /**
     * 获取指定日期的学习进度
     */
    suspend fun getDailyProgress(date: String): DailyProgress
    
    /**
     * 获取最近N天的学习进度
     */
    suspend fun getRecentProgress(days: Int): List<DailyProgress>
    
    /**
     * 获取学习连续天数
     */
    suspend fun getStudyStreak(): Int
    
    /**
     * 获取最佳成绩
     */
    suspend fun getBestScore(type: StudyType): Int
    
    /**
     * 获取平均成绩
     */
    suspend fun getAverageScore(type: StudyType): Double
    
    /**
     * 清理过期数据
     */
    suspend fun cleanupOldSessions(beforeDate: Long): Result<Int>
}

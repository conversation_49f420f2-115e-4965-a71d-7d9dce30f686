package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 生字数据模型
 * 用于听写生字功能
 */
@Serializable
data class Word(
    val id: String,
    val character: String,        // 汉字
    val pinyin: String,          // 拼音
    val meaning: String,         // 含义/解释
    val category: String = "默认", // 分类（如：一年级上册、二年级下册等）
    val difficulty: Int = 1,     // 难度等级 1-5
    val strokeCount: Int = 0,    // 笔画数
    val radicals: String = "",   // 部首
    val examples: List<String> = emptyList(), // 例句
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 生字分类
 */
@Serializable
data class WordCategory(
    val id: String,
    val name: String,            // 分类名称
    val description: String = "", // 分类描述
    val grade: String = "",      // 年级
    val semester: String = "",   // 学期
    val order: Int = 0,          // 排序
    val createdAt: Long = System.currentTimeMillis()
)

package com.homework.assistant.shared.data.repository

import com.homework.assistant.shared.data.model.Recitation
import com.homework.assistant.shared.data.model.RecitationCategory
import com.homework.assistant.shared.data.model.RecitationType
import kotlinx.coroutines.flow.Flow

/**
 * 背诵内容数据仓库接口
 */
interface RecitationRepository {
    
    /**
     * 获取所有背诵内容
     */
    fun getAllRecitations(): Flow<List<Recitation>>
    
    /**
     * 根据分类获取背诵内容
     */
    fun getRecitationsByCategory(categoryId: String): Flow<List<Recitation>>
    
    /**
     * 根据类型获取背诵内容
     */
    fun getRecitationsByType(type: RecitationType): Flow<List<Recitation>>
    
    /**
     * 根据ID获取背诵内容
     */
    suspend fun getRecitationById(id: String): Recitation?
    
    /**
     * 搜索背诵内容
     */
    fun searchRecitations(query: String): Flow<List<Recitation>>
    
    /**
     * 添加背诵内容
     */
    suspend fun insertRecitation(recitation: Recitation): Result<Unit>
    
    /**
     * 更新背诵内容
     */
    suspend fun updateRecitation(recitation: Recitation): Result<Unit>
    
    /**
     * 删除背诵内容
     */
    suspend fun deleteRecitation(id: String): Result<Unit>
    
    /**
     * 批量插入背诵内容
     */
    suspend fun insertRecitations(recitations: List<Recitation>): Result<Unit>
    
    /**
     * 获取所有分类
     */
    fun getAllCategories(): Flow<List<RecitationCategory>>
    
    /**
     * 添加分类
     */
    suspend fun insertCategory(category: RecitationCategory): Result<Unit>
    
    /**
     * 更新分类
     */
    suspend fun updateCategory(category: RecitationCategory): Result<Unit>
    
    /**
     * 删除分类
     */
    suspend fun deleteCategory(id: String): Result<Unit>
    
    /**
     * 获取分类下的背诵内容数量
     */
    suspend fun getRecitationCountByCategory(categoryId: String): Int
}

package com.homework.assistant.shared.data.model

import kotlinx.serialization.Serializable

/**
 * 学习会话数据模型
 * 记录每次学习活动的详细信息
 */
@Serializable
data class StudySession(
    val id: String,
    val type: StudyType,         // 学习类型
    val contentId: String,       // 内容ID（生字ID或背诵ID）
    val startTime: Long,         // 开始时间
    val endTime: Long? = null,   // 结束时间
    val duration: Long = 0,      // 持续时间（毫秒）
    val status: StudyStatus = StudyStatus.IN_PROGRESS, // 状态
    val score: Int? = null,      // 得分（0-100）
    val correctCount: Int = 0,   // 正确次数
    val totalCount: Int = 0,     // 总次数
    val mistakes: List<String> = emptyList(), // 错误记录
    val hints: List<String> = emptyList(),    // 使用的提示
    val notes: String = "",      // 备注
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 学习类型
 */
@Serializable
enum class StudyType {
    DICTATION,   // 听写
    RECITATION   // 背诵
}

/**
 * 学习状态
 */
@Serializable
enum class StudyStatus {
    IN_PROGRESS, // 进行中
    COMPLETED,   // 已完成
    PAUSED,      // 暂停
    CANCELLED    // 已取消
}

/**
 * 学习统计数据
 */
@Serializable
data class StudyStatistics(
    val totalSessions: Int = 0,      // 总学习次数
    val totalDuration: Long = 0,     // 总学习时长（毫秒）
    val averageScore: Double = 0.0,  // 平均得分
    val dictationStats: StudyTypeStats = StudyTypeStats(),
    val recitationStats: StudyTypeStats = StudyTypeStats(),
    val weeklyProgress: List<DailyProgress> = emptyList(),
    val lastStudyDate: Long? = null,
    val streak: Int = 0              // 连续学习天数
)

/**
 * 学习类型统计
 */
@Serializable
data class StudyTypeStats(
    val totalSessions: Int = 0,
    val totalDuration: Long = 0,
    val averageScore: Double = 0.0,
    val bestScore: Int = 0,
    val completedCount: Int = 0
)

/**
 * 每日进度
 */
@Serializable
data class DailyProgress(
    val date: String,            // 日期 YYYY-MM-DD
    val sessionCount: Int = 0,   // 学习次数
    val duration: Long = 0,      // 学习时长
    val averageScore: Double = 0.0
)

package com.homework.assistant.shared.voice

import com.homework.assistant.shared.voice.model.SpeechRecognitionResult
import com.homework.assistant.shared.voice.model.VoiceCommand
import kotlinx.coroutines.flow.Flow

/**
 * 语音识别服务接口
 */
interface VoiceRecognitionService {
    
    /**
     * 开始语音识别
     * @param language 识别语言，默认中文
     * @param timeout 超时时间（毫秒），默认10秒
     * @return 识别结果流
     */
    fun startRecognition(
        language: String = "zh-CN",
        timeout: Long = 10000
    ): Flow<SpeechRecognitionResult>
    
    /**
     * 停止语音识别
     */
    suspend fun stopRecognition()
    
    /**
     * 检查是否正在识别
     */
    fun isRecognizing(): Boolean
    
    /**
     * 检查语音识别权限
     */
    suspend fun checkPermission(): Boolean
    
    /**
     * 请求语音识别权限
     */
    suspend fun requestPermission(): Boolean
    
    /**
     * 设置识别参数
     */
    fun setRecognitionSettings(
        enableNoiseSuppression: Bo<PERSON>an = true,
        enableEchoCancellation: Boolean = true,
        enableAutoGainControl: Boolean = true
    )
}

/**
 * 语音指令识别服务接口
 */
interface VoiceCommandService {
    
    /**
     * 识别语音指令
     * @param text 语音识别的文本
     * @return 识别的指令，如果无法识别则返回 UNKNOWN 类型
     */
    suspend fun recognizeCommand(text: String): VoiceCommand
    
    /**
     * 批量识别语音指令
     */
    suspend fun recognizeCommands(texts: List<String>): List<VoiceCommand>
    
    /**
     * 添加自定义指令模式
     */
    fun addCustomCommandPattern(pattern: String, commandType: String)
    
    /**
     * 获取支持的指令列表
     */
    fun getSupportedCommands(): List<String>
    
    /**
     * 设置指令识别的置信度阈值
     */
    fun setConfidenceThreshold(threshold: Float)
}

package com.homework.assistant.shared.dictation.impl

import com.homework.assistant.shared.data.model.Word
import com.homework.assistant.shared.data.model.StudySession
import com.homework.assistant.shared.data.model.StudyType
import com.homework.assistant.shared.data.model.StudyStatus
import com.homework.assistant.shared.dictation.*
import com.homework.assistant.shared.voice.TextToSpeechService
import com.homework.assistant.shared.voice.VoiceCommandService
import com.homework.assistant.shared.voice.model.TextToSpeechRequest
import com.homework.assistant.shared.voice.model.VoiceCommandType
import com.homework.assistant.shared.utils.IdGenerator
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 听写服务实现
 */
class DictationServiceImpl(
    private val textToSpeechService: TextToSpeechService,
    private val voiceCommandService: VoiceCommandService
) : DictationService {
    
    private var currentSession: DictationSession? = null
    private val _dictationState = MutableStateFlow(
        DictationState(
            session = null,
            currentWord = null,
            progress = DictationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        )
    )
    
    override suspend fun startDictation(
        words: List<Word>,
        settings: DictationSettings
    ): DictationSession {
        val session = DictationSession(
            id = IdGenerator.generateSessionId(),
            words = words,
            settings = settings,
            currentIndex = 0,
            state = DictationSessionState.READY
        )
        
        currentSession = session
        updateState()
        
        // 开始播报第一个生字
        speakCurrentWord()
        
        return session
    }
    
    override fun getDictationState(): Flow<DictationState> {
        return _dictationState.asStateFlow()
    }
    
    override suspend fun speakCurrentWord(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        val word = getCurrentWord() ?: return Result.failure(Exception("没有当前生字"))
        
        session.state = DictationSessionState.SPEAKING
        updateState()
        
        val request = TextToSpeechRequest(
            text = word.character,
            speed = session.settings.speakingSpeed
        )
        
        return try {
            val result = textToSpeechService.speak(request)
            if (result.success) {
                session.state = DictationSessionState.LISTENING
                updateState()
                Result.success(Unit)
            } else {
                Result.failure(Exception(result.error ?: "语音播放失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun repeatCurrentWord(): Result<Unit> {
        return speakCurrentWord()
    }
    
    override suspend fun speakWordPinyin(): Result<Unit> {
        val word = getCurrentWord() ?: return Result.failure(Exception("没有当前生字"))
        
        val request = TextToSpeechRequest(
            text = "拼音是：${word.pinyin}",
            speed = currentSession?.settings?.speakingSpeed ?: 1.0f
        )
        
        return try {
            val result = textToSpeechService.speak(request)
            if (result.success) {
                Result.success(Unit)
            } else {
                Result.failure(Exception(result.error ?: "拼音播放失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun showWordMeaning(): Result<String> {
        val word = getCurrentWord() ?: return Result.failure(Exception("没有当前生字"))
        return Result.success(word.meaning)
    }
    
    override suspend fun nextWord(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        
        if (session.currentIndex < session.words.size - 1) {
            session.currentIndex++
            updateState()
            speakCurrentWord()
            return Result.success(Unit)
        } else {
            return Result.failure(Exception("已经是最后一个生字"))
        }
    }
    
    override suspend fun previousWord(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        
        if (session.currentIndex > 0) {
            session.currentIndex--
            updateState()
            speakCurrentWord()
            return Result.success(Unit)
        } else {
            return Result.failure(Exception("已经是第一个生字"))
        }
    }
    
    override suspend fun submitAnswer(answer: String): Result<DictationResult> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        val word = getCurrentWord() ?: return Result.failure(Exception("没有当前生字"))
        
        session.state = DictationSessionState.CHECKING
        updateState()
        
        val isCorrect = checkAnswer(word, answer)
        val score = calculateScore(word, answer, isCorrect)
        
        val result = DictationResult(
            word = word,
            userAnswer = answer,
            isCorrect = isCorrect,
            score = score,
            timeSpent = 5000, // 简化实现，实际应该计算真实用时
            feedback = if (isCorrect) "正确！" else "错误，正确答案是：${word.character}"
        )
        
        session.results.add(result)
        
        // 自动进入下一个生字
        if (session.settings.autoNext && session.currentIndex < session.words.size - 1) {
            nextWord()
        } else if (session.currentIndex >= session.words.size - 1) {
            session.state = DictationSessionState.COMPLETED
            updateState()
        } else {
            session.state = DictationSessionState.LISTENING
            updateState()
        }
        
        return Result.success(result)
    }
    
    override suspend fun pauseDictation(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        session.state = DictationSessionState.PAUSED
        updateState()
        return Result.success(Unit)
    }
    
    override suspend fun resumeDictation(): Result<Unit> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        session.state = DictationSessionState.LISTENING
        updateState()
        return Result.success(Unit)
    }
    
    override suspend fun finishDictation(): Result<StudySession> {
        val session = currentSession ?: return Result.failure(Exception("没有活动的听写会话"))
        
        session.state = DictationSessionState.COMPLETED
        session.endTime = System.currentTimeMillis()
        
        val studySession = StudySession(
            id = session.id,
            type = StudyType.DICTATION,
            contentId = "dictation_${session.id}",
            startTime = session.startTime,
            endTime = session.endTime,
            duration = (session.endTime ?: System.currentTimeMillis()) - session.startTime,
            status = StudyStatus.COMPLETED,
            score = calculateOverallScore(session),
            correctCount = session.results.count { it.isCorrect },
            totalCount = session.results.size
        )
        
        currentSession = null
        updateState()
        
        return Result.success(studySession)
    }
    
    override suspend fun getProgress(): DictationProgress {
        val session = currentSession ?: return DictationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        
        val correctCount = session.results.count { it.isCorrect }
        val wrongCount = session.results.count { !it.isCorrect }
        val elapsedTime = System.currentTimeMillis() - session.startTime
        val averageTime = if (session.results.isNotEmpty()) {
            session.results.map { it.timeSpent }.average().toLong()
        } else 0L
        
        return DictationProgress(
            currentIndex = session.currentIndex,
            totalCount = session.words.size,
            correctCount = correctCount,
            wrongCount = wrongCount,
            skippedCount = 0,
            hintsUsed = session.results.sumOf { it.hintsUsed },
            elapsedTime = elapsedTime,
            averageTimePerWord = averageTime
        )
    }
    
    override suspend fun handleVoiceCommand(command: String): Result<Unit> {
        val voiceCommand = voiceCommandService.recognizeCommand(command)
        
        return when (voiceCommand.type) {
            VoiceCommandType.NEXT_WORD -> nextWord()
            VoiceCommandType.PREVIOUS_WORD -> previousWord()
            VoiceCommandType.REPEAT_WORD -> repeatCurrentWord()
            VoiceCommandType.SPELL_WORD -> speakWordPinyin()
            VoiceCommandType.SHOW_MEANING -> {
                showWordMeaning()
                Result.success(Unit)
            }
            VoiceCommandType.PAUSE_DICTATION -> pauseDictation()
            VoiceCommandType.RESUME_DICTATION -> resumeDictation()
            VoiceCommandType.FINISH_DICTATION -> {
                finishDictation()
                Result.success(Unit)
            }
            else -> Result.failure(Exception("不支持的语音指令"))
        }
    }
    
    private fun getCurrentWord(): Word? {
        val session = currentSession ?: return null
        return if (session.currentIndex < session.words.size) {
            session.words[session.currentIndex]
        } else null
    }
    
    private suspend fun updateState() {
        val session = currentSession
        val currentWord = getCurrentWord()
        val progress = if (session != null) {
            getProgress()
        } else {
            DictationProgress(0, 0, 0, 0, 0, 0, 0, 0)
        }
        
        _dictationState.value = DictationState(
            session = session,
            currentWord = currentWord,
            isPlaying = session?.state == DictationSessionState.SPEAKING,
            isListening = session?.state == DictationSessionState.LISTENING,
            lastResult = session?.results?.lastOrNull(),
            progress = progress,
            availableCommands = getAvailableCommands()
        )
    }
    
    private fun checkAnswer(word: Word, answer: String): Boolean {
        // 简单实现，实际应该使用更智能的比较
        return word.character.trim() == answer.trim()
    }
    
    private fun calculateScore(word: Word, answer: String, isCorrect: Boolean): Int {
        // 简单实现，实际应该考虑更多因素
        return if (isCorrect) 100 else 0
    }
    
    private fun calculateOverallScore(session: DictationSession): Int {
        if (session.results.isEmpty()) return 0
        return session.results.map { it.score }.average().toInt()
    }
    
    private fun getAvailableCommands(): List<String> {
        return listOf("下一个", "上一个", "重复", "拼音", "含义", "暂停", "继续", "结束")
    }
}

import org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidLibrary)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.kotlinSerialization)
    // 暂时移除这些插件
    // alias(libs.plugins.ksp)
    // alias(libs.plugins.room)
}

kotlin {
    androidTarget {
        @OptIn(ExperimentalKotlinGradlePluginApi::class)
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }
    
    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = "Shared"
            isStatic = true
        }
    }
    
    // 鸿蒙 Next 支持 - 暂时注释掉，等基础项目构建成功后再启用
    // ohosArm64 {
    //     binaries.sharedLib {
    //         baseName = "homework_assistant"
    //         linkerOpts("-L${projectDir}/libs/", "-lskia")
    //         export(libs.compose.multiplatform.export)
    //     }
    // }
    
    sourceSets {
        commonMain.dependencies {
            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation(compose.material3)
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)

            implementation(libs.kotlinx.coroutines.core)
            implementation(libs.kotlinx.serialization.json)
            implementation(libs.kotlinx.datetime)

            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.serialization.kotlinx.json)
            implementation(libs.ktor.client.logging)

            // 暂时简化依赖，先让基本项目构建成功
            // implementation(libs.androidx.lifecycle.viewmodel)
            // implementation(libs.androidx.lifecycle.runtime.compose)
            // implementation(libs.androidx.navigation.compose)

            // implementation(libs.koin.core)
            // implementation(libs.koin.compose)

            // implementation(libs.room.runtime)
            // implementation(libs.room.ktx)

            // implementation(libs.datastore.preferences)
        }

        commonTest.dependencies {
            implementation(libs.kotlin.test)
        }
        
        androidMain.dependencies {
            implementation(compose.preview)
            implementation(libs.androidx.activity.compose)
            implementation(libs.ktor.client.android)
            // implementation(libs.koin.android)
            implementation(libs.kotlinx.coroutines.android)

            // 相机和图像处理 - 暂时注释掉
            // implementation(libs.camera.core)
            // implementation(libs.camera.camera2)
            // implementation(libs.camera.lifecycle)
            // implementation(libs.camera.view)
            // implementation(libs.mlkit.text.recognition)
        }
        
        iosMain.dependencies {
            implementation(libs.ktor.client.darwin)
        }
        
        // 鸿蒙 Next 依赖 - 暂时注释掉
        // val ohosArm64Main by getting {
        //     dependencies {
        //         api(libs.compose.multiplatform.export)
        //     }
        // }
    }
}

android {
    namespace = "com.homework.assistant.shared"
    compileSdk = libs.versions.agp.get().substringBefore('.').toInt() + 26
    
    defaultConfig {
        minSdk = 24
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

// 暂时移除 Room 配置
// room {
//     schemaDirectory("$projectDir/schemas")
// }

// dependencies {
//     add("kspCommonMainMetadata", libs.room.compiler)
// }

// tasks.withType<org.jetbrains.kotlin.gradle.dsl.KotlinCompile<*>>().configureEach {
//     if (name != "kspCommonMainKotlinMetadata") {
//         dependsOn("kspCommonMainKotlinMetadata")
//     }
// }

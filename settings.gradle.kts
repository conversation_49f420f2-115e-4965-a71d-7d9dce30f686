rootProject.name = "HomeworkAssistantKMP"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        // 暂时移除 OvCompose 仓库，使用标准 Compose Multiplatform
        // maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
        // maven("https://maven.pkg.jetbrains.space/kotlin/p/kotlin/dev")
    }
}

dependencyResolutionManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        // 暂时移除 OvCompose 仓库，使用标准 Compose Multiplatform
        // maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
        // maven("https://maven.pkg.jetbrains.space/kotlin/p/kotlin/dev")
    }
}

include(":composeApp")
include(":shared")

# Gradle 配置
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true

# Kotlin 配置
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.multiplatform=true
kotlin.native.ignoreDisabledTargets=true

# Android 配置
android.useAndroidX=true
android.nonTransitiveRClass=true

# Compose 配置
compose.experimental.uikit.enabled=true
compose.experimental.cinterop.enabled=true

# 项目版本配置
kotlin.version=2.0.0
compose.version=1.6.11
agp.version=8.5.2

# 平台配置 - 暂时只支持 Android 和 iOS
compose.platforms=android,ios

# 一个小学生作业助手项目

## 功能

- 听写生字
- 背诵

支持添加生字，添加要背诵的内容。
听字时，允许实时对话，比如，问某个字怎么写，支持对话时说听第 n 个词，下个词，上个词等指令，因此需要有 AI 进行语义识别，然后调用对应的 api 来实现。写完后，支持拍照检查是否正确，拍照后，在原图进行批改。并给出文本（可读）结果。



背诵也支持实时对话，如果卡顿超时（比如 5 秒，可设置），则出一两个字提示（可设置），需能自动识别是否已经背诵完成，也要能识别出用户说的“背完了”等指令。因此，也需要由 AI 进行语义识别，再调用对应的 api 来实现。背诵完毕也需给出文本（可读）结果。


## 技术栈

使用跨平台的 compose 技术，由于需支持鸿蒙 Next 系统，使用第三方的 OvCompose 框架，它是开源的，相关仓库在下面，项目构建和初始化请参照它们，如出问题，也可在这上面寻找答案：

- https://github.com/Tencent-TDS/ovCompose-multiplatform-core
- https://github.com/Tencent-TDS/ovCompose-sample
